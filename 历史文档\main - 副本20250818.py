import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
from openpyxl.utils import get_column_letter, column_index_from_string
from copy import copy
from datetime import datetime
import os
import shutil
import tempfile
# from tqdm import tqdm  # 注释掉tqdm导入

def safe_save_workbook(workbook, filename, max_retries=3, silent=True):
    """安全保存Excel文件，防止文件损坏 - 针对中文路径优化"""
    import gc
    import time

    # 强制垃圾回收，释放内存
    gc.collect()

    # 使用系统临时目录避免中文路径问题
    temp_dir = tempfile.gettempdir()
    temp_filename = os.path.join(temp_dir, f"salary_temp_{int(time.time())}.xlsx")
    backup_filename = f"{filename}.backup"

    if not silent:
        print(f"正在保存文件: {filename}")
        print(f"使用临时文件: {temp_filename}")

    # 如果目标文件存在，先创建备份
    if os.path.exists(filename):
        try:
            shutil.copy2(filename, backup_filename)
            if not silent:
                print(f"已创建备份文件: {backup_filename}")
        except Exception as e:
            if not silent:
                print(f"警告: 无法创建备份文件: {e}")

    # 尝试保存到临时文件
    for attempt in range(max_retries):
        try:
            if not silent:
                print(f"正在保存 (尝试 {attempt + 1}/{max_retries})...")

            # 1. 保存到系统临时目录
            workbook.save(temp_filename)
            if not silent:
                print("临时文件保存完成，正在验证...")

            # 2. 验证临时文件
            test_wb = openpyxl.load_workbook(temp_filename)
            sheet_count = len(test_wb.sheetnames)
            test_wb.close()
            if not silent:
                print(f"临时文件验证成功，包含 {sheet_count} 个工作表")

            # 3. 复制到目标位置
            if os.path.exists(filename):
                os.remove(filename)

            shutil.copy2(temp_filename, filename)
            if not silent:
                print("文件复制完成，正在最终验证...")

            # 4. 最终验证
            final_wb = openpyxl.load_workbook(filename)
            final_sheet_count = len(final_wb.sheetnames)
            final_wb.close()

            if final_sheet_count == sheet_count:
                if not silent:
                    print(f"✅ 文件保存成功: {filename}")

                # 清理临时文件和备份
                try:
                    os.remove(temp_filename)
                    if os.path.exists(backup_filename):
                        os.remove(backup_filename)
                except:
                    pass

                return True
            else:
                raise Exception(f"最终验证失败: 工作表数量不匹配")

        except Exception as e:
            if not silent:
                print(f"❌ 保存失败 (尝试 {attempt + 1}): {e}")

            # 清理临时文件
            try:
                if os.path.exists(temp_filename):
                    os.remove(temp_filename)
            except:
                pass

            if attempt == max_retries - 1:
                # 最后一次尝试失败，尝试从备份恢复
                if os.path.exists(backup_filename):
                    try:
                        if os.path.exists(filename):
                            os.remove(filename)
                        shutil.copy2(backup_filename, filename)
                        if not silent:
                            print(f"已从备份恢复文件: {filename}")
                    except Exception as restore_error:
                        if not silent:
                            print(f"备份恢复也失败: {restore_error}")

                raise Exception(f"文件保存失败，已尝试 {max_retries} 次: {e}")

            # 等待一秒后重试
            time.sleep(1)

    return False


def tqdm(iterable=None, desc="", total=None, ncols=None):
    """简单的tqdm替代函数，支持更多参数"""
    if iterable is None:
        # 如果没有提供iterable，返回一个上下文管理器
        return TqdmContext(total=total, desc=desc)
    
    if hasattr(iterable, '__len__'):
        total = len(iterable)
    
    for i, item in enumerate(iterable):
        if total:
            percentage = int((i + 1) / total * 100)
            bar_length = 40
            filled_length = int(bar_length * (i + 1) // total)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f'\r{desc}: {percentage:3d}%|{bar}| {i+1}/{total}', end='', flush=True)
        yield item
    
    if total:
        print()  # 完成时换行

class TqdmContext:
    """tqdm上下文管理器的简单实现"""
    def __init__(self, total=None, desc=""):
        self.total = total
        self.desc = desc
        self.current = 0
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.total:
            print()  # 完成时换行
    
    def update(self, n=1):
        self.current += n
        if self.total:
            percentage = int(self.current / self.total * 100)
            bar_length = 40
            filled_length = int(bar_length * self.current // self.total)
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f'\r{self.desc}: {percentage:3d}%|{bar}| {self.current}/{self.total}', end='', flush=True)


def find_header_row(ws):
    """定位列头行（返回行号）"""
    for row_idx, row in enumerate(ws.iter_rows(values_only=True), 1):
        if row and row[0] == '序号' and row[1] == '科室' and row[2] == '姓名' and row[3] == '绩效工资':
            return row_idx
    return None


def extract_sheet_data(ws):
    """提取 sheet 中的数据行和按科室汇总的金额"""
    header_row = find_header_row(ws)
    if not header_row:
        return [], {}

    data = []
    dept_total = {}
    for row in ws.iter_rows(min_row=header_row + 1, values_only=True):
        if not row[1] or not row[2]:
            continue

        dept = row[1]
        name = row[2]
        salary = row[3]
        if isinstance(salary, str):
            salary = 0 if not salary.replace('.', '').isdigit() else float(salary)
        elif not isinstance(salary, (int, float)):
            salary = 0
        data.append((dept, name, salary))

        if dept in dept_total:
            dept_total[dept] += salary
        else:
            dept_total[dept] = salary

    return data, dept_total


def process_total_row(ws, total_row_idx):
    """处理总计行的格式"""
    # 设置总计行的字体为粗体
    for cell in ws[total_row_idx]:
        if not isinstance(cell, openpyxl.cell.cell.MergedCell):
            # 设置字体为粗体
            cell.font = Font(
                name='Arial',
                size=16,
                bold=True  # 确保字体加粗
            )
            # 保持其他格式不变
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
            # 设置背景色
            cell.fill = PatternFill(start_color='BDD7EE', end_color='BDD7EE', fill_type='solid')


def update_summary_sheet(wb):
    """按 Sheet 名称顺序处理（排除汇总表）"""
    if '汇总表' not in wb.sheetnames:
        summary_ws = wb.create_sheet('汇总表', 0)
        summary_ws.append(['序号', '科室', '姓名', '绩效工资'])
    else:
        summary_ws = wb['汇总表']
        for row in summary_ws.iter_rows(min_row=2):
            for cell in row:
                cell.value = None

    row_idx = 2
    total_sum = 0

    # 按 Sheet 名称的自然顺序排序（Sheet1, Sheet2...）
    sheet_names = [name for name in wb.sheetnames if name != '汇总表']
    for sheet_name in sheet_names:
        ws = wb[sheet_name]
        data, dept_total = extract_sheet_data(ws)
        if not data:
            continue

        sorted_depts = sorted(dept_total.keys())
        for dept in sorted_depts:
            dept_data = [d for d in data if d[0] == dept]
            for idx, (_, name, salary) in enumerate(dept_data, start=1):
                summary_ws.cell(row=row_idx, column=1, value=idx)
                summary_ws.cell(row=row_idx, column=2, value=dept)
                summary_ws.cell(row=row_idx, column=3, value=name)
                summary_ws.cell(row=row_idx, column=4, value=salary)
                row_idx += 1

            summary_ws.cell(row=row_idx, column=1, value=f"{dept}合计")
            summary_ws.cell(row=row_idx, column=4, value=dept_total[dept])
            total_sum += dept_total[dept]
            row_idx += 1

    summary_ws.cell(row=row_idx, column=1, value="总计")
    summary_ws.cell(row=row_idx, column=4, value=total_sum)

    # 应用样式
    bold_font = Font(bold=True)
    center_alignment = Alignment(horizontal='center', vertical='center')
    yellow_fill = PatternFill(start_color='FFFF00', end_color='FFFF00', fill_type='solid')
    for row in summary_ws.iter_rows():
        for cell in row:
            cell.alignment = center_alignment
            if '合计' in str(cell.value) or cell.value == '总计':
                for cell_in_row in summary_ws[row[0].row]:
                    cell_in_row.font = bold_font
                    cell_in_row.alignment = center_alignment
                summary_ws.row_dimensions[row[0].row].height = 20
                # 标黄合计行的绩效工资单元格
                summary_ws.cell(row=row[0].row, column=4).fill = yellow_fill

    # 调整所有列的列宽为 15
    for col in 'ABCD':
        summary_ws.column_dimensions[col].width = 15

    # 设置默认活动工作表为汇总表
    wb.active = wb['汇总表']


def copy_sheet(source_wb, source_sheet_name, target_wb, target_sheet_name):
    """将一个工作簿中工作表的内容复制到另一个工作簿的工作表中"""
    source_sheet = source_wb[source_sheet_name]
    target_sheet = target_wb[target_sheet_name]

    # 复制列宽
    for col in source_sheet.columns:
        col_letter = get_column_letter(col[0].column)
        target_sheet.column_dimensions[col_letter].width = source_sheet.column_dimensions[col_letter].width

    # 清空目标工作表内容
    for row in target_sheet.iter_rows():
        for cell in row:
            cell.value = None

    # 复制单元格内容和样式
    for row in source_sheet.iter_rows():
        target_row = row[0].row
        for cell in row:
            target_cell = target_sheet.cell(row=target_row, column=cell.column)
            
            # 在设置单元格值之前添加类型转换
            if isinstance(cell.value, str) and cell.value.replace('.', '').isdigit():
                target_cell.value = float(cell.value)
                target_cell.number_format = '¥#,##0.00'
            elif isinstance(cell.value, (int, float)):
                target_cell.value = cell.value
                target_cell.number_format = '¥#,##0.00'
            else:
                target_cell.value = cell.value
            
            # 复制其他样式
            if cell.has_style:
                target_cell.font = copy(cell.font)
                target_cell.border = copy(cell.border)
                target_cell.fill = copy(cell.fill)
                target_cell.protection = copy(cell.protection)
                target_cell.alignment = copy(cell.alignment)


def find_name_row(ws):
    """找到包含'姓名'的行作为首行"""
    for row_idx, row in enumerate(ws.iter_rows(values_only=True), 1):
        for cell in row:
            if cell == '姓名':
                return row_idx
    return None


def is_merged_cell(ws, row, col):
    """检查单元格是否为合并单元格"""
    for merged_range in ws.merged_cells.ranges:
        if (row, col) in [(r, c) for r in range(merged_range.min_row, merged_range.max_row + 1) for c in range(merged_range.min_col, merged_range.max_col + 1)]:
            return merged_range.min_row, merged_range.min_col
    return None


def find_column_by_header(ws, header_row, header_name):
    """找到指定表头所在的列号"""
    for cell in ws[header_row]:
        if cell.value == header_name:
            return cell.column
    return None


def convert_formulas_to_values(ws):
    """将工作表中的公式转换为静态值"""
    for row in ws.iter_rows():
        for cell in row:
            if cell.value is not None:
                # 如果单元格有公式，获取其计算结果并设置为静态值
                if cell.data_type == 'f':  # 'f' 表示公式
                    value = cell.value
                    cell.value = value


def fill_employee_info(target_wb):
    """将'岗位工资'sheet中的员工信息填充到'工资表汇总表1'sheet中对应的列"""
    summary_ws = target_wb['工资表汇总表1']
    position_ws = target_wb['岗位工资']

    # 先将岗位工资表中的公式转换为静态值
    convert_formulas_to_values(position_ws)

    summary_header_row = find_name_row(summary_ws)
    position_header_row = find_name_row(position_ws)

    if summary_header_row and position_header_row:
        # 获取需要填充的列号
        summary_cols = {
            '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
            '工号': find_column_by_header(summary_ws, summary_header_row, '工号'),
            '身份证号': find_column_by_header(summary_ws, summary_header_row, '身份证号'),
            '岗位工资': find_column_by_header(summary_ws, summary_header_row, '岗位工资')
        }

        position_cols = {
            '姓名': find_column_by_header(position_ws, position_header_row, '姓名'),
            '工号': find_column_by_header(position_ws, position_header_row, '工号'),
            '身份证号': find_column_by_header(position_ws, position_header_row, '身份证号'),
            '岗位工资': find_column_by_header(position_ws, position_header_row, '岗位工资'),
            '人员类别': find_column_by_header(position_ws, position_header_row, '人员类别')
        }

        # 先清洗岗位工资表中的岗位工资列
        if position_cols['岗位工资']:
            for row in position_ws.iter_rows(min_row=position_header_row + 1):
                salary_cell = row[position_cols['岗位工资'] - 1]
                if salary_cell.value is not None:
                    try:
                        # 处理可能的字符串格式
                        if isinstance(salary_cell.value, str):
                            # 移除所有非数字字符（保留小数点）
                            value_str = ''.join(c for c in salary_cell.value if c.isdigit() or c == '.')
                            value = float(value_str) if value_str else 0
                        else:
                            value = float(salary_cell.value)
                        # 四舍五入到两位小数
                        salary_cell.value = round(value, 2)
                        # 设置为数字格式，带千分位和两位小数
                        salary_cell.number_format = '#,##0.00'
                    except (ValueError, TypeError):
                        salary_cell.value = 0.00
                        salary_cell.number_format = '#,##0.00'

        # 从岗位工资表中找到姓名行，从总表的第三行开始填充
        start_row = summary_header_row + 2
        
        for row in position_ws.iter_rows(min_row=position_header_row + 1):
            name = row[position_cols['姓名'] - 1].value
            employee_type = row[position_cols['人员类别'] - 1].value if position_cols['人员类别'] else None
            
            # 处理人员类别为"编外"或"产假"的员工
            if name and (employee_type == "编外" or employee_type == "产假"):
                # 填充每个字段
                for field in ['姓名', '工号', '身份证号', '岗位工资']:
                    if summary_cols[field] and position_cols[field]:
                        source_cell = row[position_cols[field] - 1]
                        target_col = summary_cols[field]
                        target_cell = summary_ws.cell(row=start_row, column=target_col)

                        # 复制值和格式
                        if field == '岗位工资':
                            # 确保工资值为浮点数并四舍五入到两位小数
                            try:
                                if isinstance(source_cell.value, str):
                                    value_str = ''.join(c for c in source_cell.value if c.isdigit() or c == '.')
                                    value = float(value_str) if value_str else 0
                                else:
                                    value = float(source_cell.value) if source_cell.value is not None else 0
                                target_cell.value = round(value, 2)
                                # 统一使用带千分位和两位小数的格式
                                target_cell.number_format = '#,##0.00'
                            except (ValueError, TypeError):
                                target_cell.value = 0.00
                                target_cell.number_format = '#,##0.00'
                        else:
                            target_cell.value = source_cell.value

                start_row += 1


def associate_performance_salary(target_wb):
    """将"绩效工资"表中的"绩效工资"列关联到"工资表汇总表1"中的绩效工资列"""
    summary_ws = target_wb['工资表汇总表1']
    performance_ws = target_wb['绩效工资']

    summary_header_row = find_name_row(summary_ws)
    performance_header_row = find_header_row(performance_ws)

    if summary_header_row and performance_header_row:
        summary_name_col = find_column_by_header(summary_ws, summary_header_row, '姓名')
        summary_performance_col = find_column_by_header(summary_ws, summary_header_row, '绩效工资')
        performance_name_col = find_column_by_header(performance_ws, performance_header_row, '姓名')
        performance_performance_col = find_column_by_header(performance_ws, performance_header_row, '绩效工资')
        
        # 获取序号列的列号
        performance_seq_col = find_column_by_header(performance_ws, performance_header_row, '序号')

        if summary_name_col and summary_performance_col and performance_name_col and performance_performance_col:
            # 先清洗绩效工资表中的绩效工资列
            for row in performance_ws.iter_rows(min_row=performance_header_row + 1):
                # 处理序号列，确保其为普通数字格式
                if performance_seq_col:
                    seq_cell = row[performance_seq_col - 1]
                    if seq_cell.value is not None:
                        # 确保序号为整数格式
                        if isinstance(seq_cell.value, (int, float)):
                            seq_cell.value = int(seq_cell.value)
                        elif isinstance(seq_cell.value, str) and seq_cell.value.isdigit():
                            seq_cell.value = int(seq_cell.value)
                        # 设置为普通数字格式，不带千分位和小数
                        seq_cell.number_format = '0'
                
                # 处理绩效工资列
                salary_cell = row[performance_performance_col - 1]
                if salary_cell.value is not None:
                    try:
                        # 处理可能的字符串格式
                        if isinstance(salary_cell.value, str):
                            # 移除所有非数字字符（保留小数点）
                            value_str = ''.join(c for c in salary_cell.value if c.isdigit() or c == '.')
                            value = float(value_str) if value_str else 0
                        else:
                            value = float(salary_cell.value)
                        # 四舍五入到两位小数
                        salary_cell.value = round(value, 2)
                        # 设置为数字格式，带千分位和两位小数
                        salary_cell.number_format = '#,##0.00'
                    except (ValueError, TypeError):
                        salary_cell.value = 0.00
                        salary_cell.number_format = '#,##0.00'

            # 构建绩效工资表的姓名和绩效工资映射
            performance_map = {}
            for row in performance_ws.iter_rows(min_row=performance_header_row + 1):
                name = row[performance_name_col - 1].value
                salary_cell = row[performance_performance_col - 1]
                if name and salary_cell.value is not None:
                    try:
                        if isinstance(salary_cell.value, str):
                            value_str = ''.join(c for c in salary_cell.value if c.isdigit() or c == '.')
                            value = float(value_str) if value_str else 0
                        else:
                            value = float(salary_cell.value)
                        performance_map[name] = round(value, 2)
                    except (ValueError, TypeError):
                        performance_map[name] = 0.00

            # 填充工资表汇总表1中的绩效工资列，从姓名行的下两行开始
            start_row = summary_header_row + 2
            for row in summary_ws.iter_rows(min_row=start_row):
                name = row[summary_name_col - 1].value
                performance_cell = row[summary_performance_col - 1]
                # 检查是否为合并单元格
                merged = is_merged_cell(summary_ws, performance_cell.row, performance_cell.column)
                if merged:
                    target_row, target_col = merged
                    target_cell = summary_ws.cell(row=target_row, column=target_col)
                else:
                    target_cell = performance_cell
                # 仅当单元格既为空，又不是"绩效工资"文本时才进行赋值
                if target_cell.value is None or target_cell.value != "绩效工资":
                    if name in performance_map:
                        target_cell.value = performance_map[name]
                    else:
                        target_cell.value = 0.00
                    # 强制使用带千分位和两位小数的格式
                    target_cell.number_format = '#,##0.00'


def is_merged_cell(ws, row, col):
    """检查合并单元格并返回左上角坐标"""
    for merged_range in ws.merged_cells.ranges:
        min_row, min_col, max_row, max_col = range_boundaries(merged_range.coord)
        if min_row <= row <= max_row and min_col <= col <= max_col:
            return (min_row, min_col)
    return None

def associate_project_info(target_wb):
    """基于岗位工资表的姓名，匹配项目支出分类表并填充科室和项目分类"""
    summary_ws = target_wb['工资表汇总表1']
    project_ws = target_wb['项目支出分类']
    position_ws = target_wb['岗位工资']

    # 获取表头行（需确保find_name_row已处理异常）
    try:
        summary_header_row = find_name_row(summary_ws)
        project_header_row = find_name_row(project_ws)
        position_header_row = find_name_row(position_ws)
    except ValueError as e:
        # 静默处理错误，不输出
        return

    # 获取列号并校验
    required_summary_cols = ['姓名', '科室', '项目分类']
    summary_cols = {col: find_column_by_header(summary_ws, summary_header_row, col) for col in required_summary_cols}
    if None in summary_cols.values():
        missing = [k for k, v in summary_cols.items() if v is None]
        raise ValueError(f"工资表汇总表1缺少列: {missing}")

    position_name_col = find_column_by_header(position_ws, position_header_row, '姓名')
    project_name_col = find_column_by_header(project_ws, project_header_row, '姓名')
    project_dept_col = find_column_by_header(project_ws, project_header_row, '科室')
    project_category_col = find_column_by_header(project_ws, project_header_row, '项目分类')

    if not all([position_name_col, project_name_col, project_dept_col, project_category_col]):
        raise ValueError("项目支出分类表或岗位工资表缺少关键列")

    # --- 核心修改1：提取岗位工资表所有姓名作为基准 ---
    position_names = set()
    for row in position_ws.iter_rows(min_row=position_header_row + 1):
        name = row[position_name_col - 1].value
        if name:
            position_names.add(name.strip())  # 去除前后空格避免匹配问题

    # --- 核心修改2：构建项目表的姓名映射 ---
    project_map = {}
    for row in project_ws.iter_rows(min_row=project_header_row + 1):
        name = row[project_name_col - 1].value
        if name:
            name = name.strip()
            project_map[name] = {
                '科室': row[project_dept_col - 1].value,
                '项目分类': row[project_category_col - 1].value
            }

    # --- 核心修改3：遍历工资表汇总表，仅处理岗位工资表中的姓名 ---
    start_row = summary_header_row + 1
    for row_idx in range(start_row, summary_ws.max_row + 1):
        summary_name = summary_ws.cell(row=row_idx, column=summary_cols['姓名']).value
        if not summary_name:
            continue

        summary_name = summary_name.strip()

        # 仅处理岗位工资表中存在的姓名
        if summary_name not in position_names:
            continue

        # 处理科室列
        dept_cell = summary_ws.cell(row=row_idx, column=summary_cols['科室'])
        merged = is_merged_cell(summary_ws, dept_cell.row, dept_cell.column)
        target_row, target_col = (merged[0], merged[1]) if merged else (dept_cell.row, dept_cell.column)
        target_dept_cell = summary_ws.cell(row=target_row, column=target_col)

        # 处理项目分类列
        category_cell = summary_ws.cell(row=row_idx, column=summary_cols['项目分类'])
        merged = is_merged_cell(summary_ws, category_cell.row, category_cell.column)
        target_row, target_col = (merged[0], merged[1]) if merged else (category_cell.row, category_cell.column)
        target_category_cell = summary_ws.cell(row=target_row, column=target_col)

        # --- 核心修改4：根据项目表匹配结果填充或清空 ---
        if summary_name in project_map:
            # 填充科室和项目分类
            target_dept_cell.value = project_map[summary_name]['科室']
            target_category_cell.value = project_map[summary_name]['项目分类']
        else:
            # 未找到匹配，清空数据
            target_dept_cell.value = None
            target_category_cell.value = None



def associate_additional_salary(target_wb):
    """关联独生子女费、护龄津贴和周六日节假日加班补助"""
    summary_ws = target_wb['工资表汇总表1']
    child_ws = target_wb['独生子女费名单']
    nurse_ws = target_wb['编外护士护龄津贴发放表']
    overtime_ws = target_wb['周六日节假日加班']

    summary_header_row = find_name_row(summary_ws)
    child_header_row = find_name_row(child_ws)
    nurse_header_row = find_name_row(nurse_ws)
    overtime_header_row = find_name_row(overtime_ws)

    # 添加周六日节假日加班补助表头（如果不存在）
    overtime_col = find_column_by_header(summary_ws, summary_header_row, '周六日节假日加班补助')
    if not overtime_col:
        # 在第24列（X列）添加表头
        overtime_col = 24
        summary_ws.cell(row=summary_header_row, column=overtime_col, value='周六日节假日加班补助')


    if not all([summary_header_row, child_header_row, nurse_header_row, overtime_header_row]):
        return

    # 获取汇总表中的列号
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '独生子女费': find_column_by_header(summary_ws, summary_header_row, '独生子女费'),
        '护龄津贴': find_column_by_header(summary_ws, summary_header_row, '护龄津贴'),
        '周六日节假日加班补助': overtime_col  # 使用我们刚刚设置的列
    }

    # 获取各表中的列号
    child_cols = {
        '姓名': find_column_by_header(child_ws, child_header_row, '姓名'),
        '金额': find_column_by_header(child_ws, child_header_row, '金额')
    }

    nurse_cols = {
        '姓名': find_column_by_header(nurse_ws, nurse_header_row, '姓名'),
        '金额': find_column_by_header(nurse_ws, nurse_header_row, '金额（元）')
    }

    overtime_cols = {
        '姓名': find_column_by_header(overtime_ws, overtime_header_row, '姓名'),
        '金额': find_column_by_header(overtime_ws, overtime_header_row, '金额'),
        '合计金额': find_column_by_header(overtime_ws, overtime_header_row, '合计金额'),
        '合计': find_column_by_header(overtime_ws, overtime_header_row, '合计'),
        '总计': find_column_by_header(overtime_ws, overtime_header_row, '总计')
    }

    if not all(summary_cols.values()):
        return

    # 创建数据映射
    data_maps = {
        '独生子女费': {},
        '护龄津贴': {},
        '周六日节假日加班补助': {}
    }

    # 读取独生子女费数据
    if all(child_cols.values()):
        for row in child_ws.iter_rows(min_row=child_header_row + 1):
            name = row[child_cols['姓名'] - 1].value
            if name:
                value = row[child_cols['金额'] - 1].value
                if isinstance(value, (int, float)) and value != 0:
                    data_maps['独生子女费'][name] = row[child_cols['金额'] - 1]
                else:
                    try:
                        numeric_value = float(value) if value else 0
                        if numeric_value != 0:
                            data_maps['独生子女费'][name] = row[child_cols['金额'] - 1]
                    except (ValueError, TypeError):
                        pass

    # 读取护龄津贴数据
    if all(nurse_cols.values()):
        for row in nurse_ws.iter_rows(min_row=nurse_header_row + 1):
            name = row[nurse_cols['姓名'] - 1].value
            if name:
                value = row[nurse_cols['金额'] - 1].value
                if isinstance(value, (int, float)) and value != 0:
                    data_maps['护龄津贴'][name] = row[nurse_cols['金额'] - 1]
                else:
                    try:
                        numeric_value = float(value) if value else 0
                        if numeric_value != 0:
                            data_maps['护龄津贴'][name] = row[nurse_cols['金额'] - 1]
                    except (ValueError, TypeError):
                        pass

    # 读取加班补助数据 - 使用H列（第8列）的数据
    if overtime_cols['姓名']:
        # 从第3行开始读取数据（跳过表头和子表头）
        data_start_row = 3  # 直接从第3行开始，因为第1行是表头，第2行是子表头
        h_column = 8  # H列是第8列


        for row_idx in range(data_start_row, overtime_ws.max_row + 1):
            name_cell = overtime_ws.cell(row=row_idx, column=3)  # C列姓名
            amount_cell = overtime_ws.cell(row=row_idx, column=h_column)  # H列金额

            if name_cell.value and str(name_cell.value).strip():
                name = str(name_cell.value).strip()

                # 检查H列的加班金额
                if amount_cell.value is not None:
                    try:
                        amount = float(amount_cell.value)
                        if amount != 0:  # 只添加金额不为零的数据
                            data_maps['周六日节假日加班补助'][name] = amount_cell

                    except (ValueError, TypeError):
                        pass



    # 填充数据到汇总表
    start_row = summary_header_row + 2
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if not name:
            continue

        # 处理每种补贴
        for field, field_map in [
            ('独生子女费', data_maps['独生子女费']),
            ('护龄津贴', data_maps['护龄津贴']),
            ('周六日节假日加班补助', data_maps['周六日节假日加班补助'])
        ]:
            target_cell = row[summary_cols[field] - 1]
            merged = is_merged_cell(summary_ws, target_cell.row, target_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)

            if name in field_map:
                source_cell = field_map[name]
                value = source_cell.value

                if isinstance(value, (int, float)):
                    target_cell.value = value
                    target_cell.number_format = '#,##0.00'
                else:
                    try:
                        target_cell.value = float(value) if value else None
                        target_cell.number_format = '#,##0.00'
                    except (ValueError, TypeError):
                        target_cell.value = value

                if source_cell.has_style:
                    target_cell.font = copy(source_cell.font)
                    target_cell.border = copy(source_cell.border)
                    target_cell.fill = copy(source_cell.fill)
                    target_cell.protection = copy(source_cell.protection)
                    target_cell.alignment = copy(source_cell.alignment)
            else:
                # 如果不在映射中，设置为空
                target_cell.value = None


def associate_rent(target_wb):
    """将"水电、房租"表中的房租和水电费关联到"工资表汇总表1"中的对应列，并转换为负数"""
    summary_ws = target_wb['工资表汇总表1']
    rent_ws = target_wb['水电、房租']

    summary_header_row = find_name_row(summary_ws)
    rent_header_row = find_name_row(rent_ws)

    if not all([summary_header_row, rent_header_row]):
        return

    # 获取列号（房租固定在Q列，即第17列；水电费固定在R列，即第18列）
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '房租': 17,  # Q列
        '水电费': 18   # R列
    }

    rent_cols = {
        '姓名': find_column_by_header(rent_ws, rent_header_row, '姓名'),
        '房租': find_column_by_header(rent_ws, rent_header_row, '房租'),
        '水电费': find_column_by_header(rent_ws, rent_header_row, '水电费')
    }

    if not all(summary_cols.values()) or not all(rent_cols.values()):
        return

    # 创建房租和水电费数据映射
    rent_map = {}
    utility_map = {}
    for row in rent_ws.iter_rows(min_row=rent_header_row + 1):
        name = row[rent_cols['姓名'] - 1].value
        if name:
            rent_cell = row[rent_cols['房租'] - 1]
            utility_cell = row[rent_cols['水电费'] - 1]
            rent_map[name] = rent_cell
            utility_map[name] = utility_cell

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name in rent_map:
            # 处理房租
            rent_cell = row[summary_cols['房租'] - 1]
            merged = is_merged_cell(summary_ws, rent_cell.row, rent_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)
            else:
                target_cell = rent_cell

            source_cell = rent_map[name]
            value = source_cell.value

            if isinstance(value, (int, float)):
                target_cell.value = -abs(value)  # 确保为负数
                target_cell.number_format = '#,##0.00'
            else:
                try:
                    numeric_value = float(value) if value else 0
                    target_cell.value = -abs(numeric_value)  # 确保为负数
                    target_cell.number_format = '#,##0.00'
                except (ValueError, TypeError):
                    target_cell.value = value

            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)

            # 处理水电费
            utility_cell = row[summary_cols['水电费'] - 1]
            merged = is_merged_cell(summary_ws, utility_cell.row, utility_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)
            else:
                target_cell = utility_cell

            source_cell = utility_map[name]
            value = source_cell.value

            if isinstance(value, (int, float)):
                target_cell.value = -abs(value)  # 确保为负数
                target_cell.number_format = '#,##0.00'
            else:
                try:
                    numeric_value = float(value) if value else 0
                    target_cell.value = -abs(numeric_value)  # 确保为负数
                    target_cell.number_format = '#,##0.00'
                except (ValueError, TypeError):
                    target_cell.value = value

            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)


def associate_housing_fund(target_wb):
    """将"公积金"表中的数据关联到"工资表汇总表1"中"""
    summary_ws = target_wb['工资表汇总表1']
    fund_ws = target_wb['公积金']

    summary_header_row = find_name_row(summary_ws)
    fund_header_row = find_name_row(fund_ws)

    if not all([summary_header_row, fund_header_row]):
        return

    # 获取列号（公积金固定在S列，即第19列）
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '公积金': 19,  # S列
        '个税': 20     # T列
    }

    fund_cols = {
        '姓名': find_column_by_header(fund_ws, fund_header_row, '姓名'),
        '公积金': find_column_by_header(fund_ws, fund_header_row, '单位及个人分别缴纳'),
        '个税': find_column_by_header(fund_ws, fund_header_row, '个人所得税')
    }

    if not all([summary_cols['姓名'], summary_cols['公积金']]) or not all([fund_cols['姓名'], fund_cols['公积金']]):
        return

    # 创建公积金数据映射
    fund_map = {}
    tax_map = {}
    for row in fund_ws.iter_rows(min_row=fund_header_row + 1):
        name = row[fund_cols['姓名'] - 1].value
        if name:
            # 处理公积金数据
            fund_cell = row[fund_cols['公积金'] - 1]
            fund_map[name] = fund_cell
            
            # 处理个税数据（如果存在）
            if fund_cols['个税']:
                tax_cell = row[fund_cols['个税'] - 1]
                tax_map[name] = tax_cell

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if not name:
            continue
            
        # 处理公积金列
        if name in fund_map:
            fund_cell = row[summary_cols['公积金'] - 1]
            merged = is_merged_cell(summary_ws, fund_cell.row, fund_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)
            else:
                target_cell = fund_cell

            source_cell = fund_map[name]
            value = source_cell.value

            if isinstance(value, (int, float)):
                target_cell.value = -abs(value)  # 确保为负数
                target_cell.number_format = '#,##0.00'
            else:
                try:
                    numeric_value = float(value) if value else 0
                    target_cell.value = -abs(numeric_value)  # 确保为负数
                    target_cell.number_format = '#,##0.00'
                except (ValueError, TypeError):
                    target_cell.value = value

            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)
        
        # 不在这里处理个税列，让associate_tax函数专门处理


def associate_insurance_fees(target_wb):
    """将"保险费"表中的保险费数据关联到"工资表汇总表1"中的W列，并转换为负数"""
    summary_ws = target_wb['工资表汇总表1']
    insurance_ws = target_wb['保险费']

    summary_header_row = find_name_row(summary_ws)
    insurance_header_row = find_name_row(insurance_ws)

    if not all([summary_header_row, insurance_header_row]):
        return

    # 获取列号（保险费固定在W列，即第22列）
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '保险费': 23  # W列
    }

    insurance_cols = {
        '姓名': find_column_by_header(insurance_ws, insurance_header_row, '姓名'),
        '保险费': 4  # D列
    }

    if not all(summary_cols.values()):
        return

    # 创建保险费数据映射
    insurance_map = {}
    for row in insurance_ws.iter_rows(min_row=insurance_header_row + 1):
        name = row[insurance_cols['姓名'] - 1].value
        if name:
            insurance_cell = row[insurance_cols['保险费'] - 1]
            insurance_map[name] = insurance_cell

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name in insurance_map:
            insurance_cell = row[summary_cols['保险费'] - 1]
            merged = is_merged_cell(summary_ws, insurance_cell.row, insurance_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)
            else:
                target_cell = insurance_cell

            source_cell = insurance_map[name]
            value = source_cell.value

            if isinstance(value, (int, float)):
                target_cell.value = -abs(value)  # 确保为负数
                target_cell.number_format = '#,##0.00'
            else:
                try:
                    numeric_value = float(value) if value else 0
                    target_cell.value = -abs(numeric_value)  # 确保为负数
                    target_cell.number_format = '#,##0.00'
                except (ValueError, TypeError):
                    target_cell.value = value

            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)


def associate_union_fees(target_wb):
    """将"工会会费"表中的工会会费数据关联到"工资表汇总表1"中的V列，并转换为负数"""
    summary_ws = target_wb['工资表汇总表1']
    union_ws = target_wb['工会会费']

    summary_header_row = find_name_row(summary_ws)
    union_header_row = find_name_row(union_ws)

    if not all([summary_header_row, union_header_row]):
        return

    # 获取列号（工会会费固定在U列，即第21列）
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '工会会费': 22  # V列
    }

    union_cols = {
        '姓名': find_column_by_header(union_ws, union_header_row, '姓名'),
        '工会会费': find_column_by_header(union_ws, union_header_row, '工会会费')
    }

    if not all(summary_cols.values()) or not all(union_cols.values()):
        return

    # 创建工会会费数据映射
    union_map = {}
    for row in union_ws.iter_rows(min_row=union_header_row + 1):
        name = row[union_cols['姓名'] - 1].value
        if name:
            union_cell = row[union_cols['工会会费'] - 1]
            union_map[name] = union_cell

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name in union_map:
            union_cell = row[summary_cols['工会会费'] - 1]
            merged = is_merged_cell(summary_ws, union_cell.row, union_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)
            else:
                target_cell = union_cell

            source_cell = union_map[name]
            value = source_cell.value

            if isinstance(value, (int, float)):
                target_cell.value = -abs(value)  # 确保为负数
                target_cell.number_format = '#,##0.00'
            else:
                try:
                    numeric_value = float(value) if value else 0
                    target_cell.value = -abs(numeric_value)  # 确保为负数
                    target_cell.number_format = '#,##0.00'
                except (ValueError, TypeError):
                    target_cell.value = value

            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)


def associate_tax(target_wb):
    """将"综合所得申报税款计算第一次"表中的个税数据关联到"工资表汇总表1"中的个税列（T列），并转换为负数"""
    summary_ws = target_wb['工资表汇总表1']
    tax_ws = target_wb['综合所得申报税款计算第一次']

    summary_header_row = find_name_row(summary_ws)
    tax_header_row = find_name_row(tax_ws)

    if not all([summary_header_row, tax_header_row]):
        return

    # 获取列号（个税额固定在T列，即第19列）
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '个税': 20  # T列
    }

    tax_cols = {
        '姓名': find_column_by_header(tax_ws, tax_header_row, '姓名'),
        '个税': find_column_by_header(tax_ws, tax_header_row, '应补(退)税额')
    }

    if not all(summary_cols.values()) or not all(tax_cols.values()):
        return

    # 创建个税数据映射，直接存储数值
    tax_map = {}
    for row in tax_ws.iter_rows(min_row=tax_header_row + 1):
        name = row[tax_cols['姓名'] - 1].value
        if name:
            value = row[tax_cols['个税'] - 1].value
            # 直接将数值转换为负数
            if isinstance(value, (int, float)):
                tax_map[name] = -abs(float(value))  # 确保转换为浮点数
            else:
                try:
                    numeric_value = float(value) if value else 0
                    tax_map[name] = -abs(numeric_value)
                except (ValueError, TypeError):
                    tax_map[name] = 0

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name and not any(x in str(name) for x in ['小计', '总计']):  # 跳过小计和总计行
            if name in tax_map:
                target_cell = row[summary_cols['个税'] - 1]
                merged = is_merged_cell(summary_ws, target_cell.row, target_cell.column)
                if merged:
                    target_row, target_col = merged
                    target_cell = summary_ws.cell(row=target_row, column=target_col)

                # 直接使用已转换为负数的值
                target_cell.value = tax_map[name]
                target_cell.number_format = '#,##0.00'


def calculate_net_salary(target_wb):
    """计算工资表汇总表1中的实发工资（X列）
    X列 = O列 + Q列 + R列 + S列 + T列 + U列 + V列 + W列
    """
    summary_ws = target_wb['工资表汇总表1']
    header_row = find_name_row(summary_ws)

    if not header_row:
        return

    # 获取列号
    cols = {
        'O': 15,  # O列
        'Q': 17,  # Q列
        'R': 18,  # R列
        'S': 19,  # S列
        'T': 20,  # T列
        'U': 21,  # U列
        'V': 22,  # V列
        'W': 23,  # W列
        'X': 24   # X列（实发工资）
    }

    # 从第5行开始计算
    start_row = 5
    for row in summary_ws.iter_rows(min_row=start_row):
        # 计算X列的值（O+Q+R+S+T+U+V+W的和）
        total = 0
        for col_letter in ['O', 'Q', 'R', 'S', 'T', 'U', 'V', 'W']:
            cell = row[cols[col_letter] - 1]
            if cell.value is not None:
                try:
                    value = float(cell.value) if cell.value else 0
                    total += value
                except (ValueError, TypeError):
                    pass

        # 更新X列（实发工资）
        x_cell = row[cols['X'] - 1]
        x_cell.value = total
        x_cell.number_format = '#,##0.00'


def fill_today_date(target_wb):
    """在工资表汇总表1的第二行第一列填充当天日期"""
    summary_ws = target_wb['工资表汇总表1']
    # 生成不带前导零的日期格式，如 2025/8/18
    now = datetime.now()
    today = f"{now.year}/{now.month}/{now.day}"
    cell = summary_ws.cell(row=2, column=1, value=today)
    cell.font = Font(size=16)
    cell.alignment = Alignment(horizontal='center', vertical='center')


def copy_to_project_summary(target_wb):
    """将工资表汇总表1的数据复制到工资表汇总表按项目，不复制底色，使用自己的颜色美化格式"""
    source_ws = target_wb['工资表汇总表1']
    
    # 如果目标sheet不存在，创建它
    if '工资表汇总表按项目' not in target_wb.sheetnames:
        target_wb.create_sheet('工资表汇总表按项目')
    target_ws = target_wb['工资表汇总表按项目']
    
    # 先解除目标工作表中的所有合并单元格
    for merged_range in target_ws.merged_cells.ranges:
        target_ws.unmerge_cells(str(merged_range))
    
    # 清空目标工作表内容
    for row in target_ws.iter_rows():
        for cell in row:
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                cell.value = None
    
    # 复制所有数据和样式（除了底色）
    for row in source_ws.rows:
        for cell in row:
            # 检查是否为合并单元格
            merged = is_merged_cell(source_ws, cell.row, cell.column)
            if merged:
                # 如果是合并单元格，获取合并范围
                for merged_range in source_ws.merged_cells.ranges:
                    if (cell.row, cell.column) in [(r, c) for r in range(merged_range.min_row, merged_range.max_row + 1) 
                                                 for c in range(merged_range.min_col, merged_range.max_col + 1)]:
                        # 在目标工作表中合并相同范围的单元格
                        target_ws.merge_cells(start_row=merged_range.min_row, 
                                           start_column=merged_range.min_col,
                                           end_row=merged_range.max_row,
                                           end_column=merged_range.max_col)
                        # 只复制合并单元格左上角的值和部分样式（不包括底色）
                        if cell.row == merged_range.min_row and cell.column == merged_range.min_col:
                            target_cell = target_ws.cell(row=cell.row, column=cell.column)
                            target_cell.value = cell.value
                            if cell.has_style:
                                target_cell.font = copy(cell.font)
                                target_cell.border = copy(cell.border)
                                target_cell.number_format = copy(cell.number_format)
                                target_cell.protection = copy(cell.protection)
                                target_cell.alignment = copy(cell.alignment)
                        break
            else:
                # 如果不是合并单元格，复制值和部分样式（不包括底色）
                target_cell = target_ws.cell(row=cell.row, column=cell.column)
                target_cell.value = cell.value
                if cell.has_style:
                    target_cell.font = copy(cell.font)
                    target_cell.border = copy(cell.border)
                    target_cell.number_format = copy(cell.number_format)
                    target_cell.protection = copy(cell.protection)
                    target_cell.alignment = copy(cell.alignment)
    
    # 找到表头行
    header_row = find_name_row(target_ws)
    if header_row:
        # 应用表格格式化，但不应用单元格底色
        apply_table_formatting(target_ws, header_row, apply_fill=False)
    
    # 特别处理第二行日期的格式
    date_cell = target_ws.cell(row=2, column=1)
    date_cell.font = Font(size=16)
    date_cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # 确保所有单元格居中对齐
    for row in target_ws.iter_rows():
        for cell in row:
            cell.alignment = Alignment(horizontal='center', vertical='center')
    
    # 在最后添加总计行并设置格式
    total_row_idx = target_ws.max_row
    process_total_row(target_ws, total_row_idx)
    
    # 应用自定义的颜色美化格式
    apply_custom_formatting(target_ws)

def apply_custom_formatting(ws):
    """应用自定义的颜色美化格式"""
    # 设置表头的底色（浅蓝色）
    header_row = find_name_row(ws)
    if header_row:
        for cell in ws[header_row]:
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                cell.fill = PatternFill(start_color='BDD7EE', end_color='BDD7EE', fill_type='solid')

    # 为小计行设置底色（浅灰色）
    for row in ws.iter_rows(min_row=5):  # 从第5行开始
        first_cell = row[0]
        if first_cell.value and isinstance(first_cell.value, str) and '小计' in first_cell.value:
            for cell in row:
                if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                    cell.fill = PatternFill(start_color='D9D9D9', end_color='D9D9D9', fill_type='solid')

    # 为总计行设置底色（深蓝色）
    for row in ws.iter_rows(min_row=5):  # 从第5行开始
        first_cell = row[0]
        if first_cell.value and isinstance(first_cell.value, str) and '总计' in first_cell.value:
            for cell in row:
                if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                    cell.fill = PatternFill(start_color='BDD7EE', end_color='BDD7EE', fill_type='solid')


def sort_and_subtotal_by_department(target_wb):
    """对工资表汇总表1按科室排序并添加小计"""
    ws = target_wb['工资表汇总表1']
    
    # 找到表头行
    header_row = find_name_row(ws)
    if not header_row:
        return
        
    # 找到关键列的索引
    dept_col = find_column_by_header(ws, header_row, '科室')
    if not dept_col:
        return
    
    # 为第3和第4行设置与小计行相同的底色
    subtotal_fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')  # 浅蓝色背景
    for row_idx in [3, 4]:
        for col_idx in range(1, ws.max_column + 1):
            cell = ws.cell(row=row_idx, column=col_idx)
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                cell.fill = subtotal_fill
        
    # 获取数据范围
    data_start_row = header_row + 2  # 跳过表头和日期行
    data_end_row = ws.max_row
    
    # 收集数据
    data = []
    for row in range(data_start_row, data_end_row + 1):
        row_data = []
        dept = ws.cell(row=row, column=dept_col).value
        if dept and not ('合计' in str(dept) or '小计' in str(dept)):
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                row_data.append(cell.value)
            data.append(row_data)
    
    # 按科室排序（倒序）
    data.sort(key=lambda x: str(x[dept_col-1]), reverse=True)
    
    # 清除原有数据并移除所有单元格样式
    for row in range(data_start_row, data_end_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            cell.value = None
            # 清除单元格底色
            cell.fill = PatternFill(fill_type=None)
    
    # 写回数据并添加小计
    current_row = data_start_row
    current_dept = None
    dept_start_row = current_row
    sequence_number = 1
    
    for row_data in data:
        dept = row_data[dept_col-1]
        
        # 如果科室变化，添加小计
        if current_dept and dept != current_dept:
            # 添加小计行，不显示序号
            ws.cell(row=current_row, column=2, value=None)  # 序号留空
            ws.cell(row=current_row, column=dept_col, value="小计")  # 只显示"小计"
            
            # 计算G到X列的小计
            for col in range(7, 25):  # G到X列
                sum_value = 0
                for r in range(dept_start_row, current_row):
                    cell_value = ws.cell(row=r, column=col).value
                    if isinstance(cell_value, (int, float)):
                        sum_value += cell_value
                ws.cell(row=current_row, column=col, value=sum_value)
                ws.cell(row=current_row, column=col).number_format = '#,##0.00'
            
            # 设置小计行样式
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=current_row, column=col)
                cell.font = Font(bold=True, size=16)  # 确保字体加粗
                cell.fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')  # 浅蓝色背景
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            current_row += 1
            dept_start_row = current_row
        
        # 写入数据行
        for col, value in enumerate(row_data, start=1):
            cell = ws.cell(row=current_row, column=col)
            if col == 1:  # 项目分类列保持原值
                cell.value = value
            elif col == 2:  # 序号放在B列
                cell.value = sequence_number
            else:
                cell.value = value
        
        current_dept = dept
        current_row += 1
        sequence_number += 1
    
    # 添加最后一个科室的小计
    if current_dept:
        ws.cell(row=current_row, column=2, value=None)  # 序号留空
        ws.cell(row=current_row, column=dept_col, value="小计")  # 只显示"小计"
        
        # 计算G到X列的小计
        for col in range(7, 25):  # G到X列
            sum_value = 0
            for r in range(dept_start_row, current_row):
                cell_value = ws.cell(row=r, column=col).value
                if isinstance(cell_value, (int, float)):
                    sum_value += cell_value
                ws.cell(row=current_row, column=col, value=sum_value)
                ws.cell(row=current_row, column=col).number_format = '#,##0.00'
        
        # 设置小计行样式
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=current_row, column=col)
            cell.font = Font(bold=True, size=16)  # 确保字体加粗
            cell.fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')  # 浅蓝色背景
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        current_row += 1
    
    # 添加总计行
    ws.cell(row=current_row, column=2, value=None)  # 序号留空
    ws.cell(row=current_row, column=dept_col, value="总计")  # 总计放在科室列
    
    # 计算G到X列的总计
    for col in range(7, 25):  # G到X列
        sum_value = 0
        for r in range(data_start_row, current_row):
            cell_value = ws.cell(row=r, column=col).value
            if isinstance(cell_value, (int, float)) and not any(x in str(ws.cell(row=r, column=dept_col).value) for x in ['小计', '总计']):
                sum_value += cell_value
        ws.cell(row=current_row, column=col, value=sum_value)
        ws.cell(row=current_row, column=col).number_format = '#,##0.00'
    
    # 处理总计行格式
    process_total_row(ws, current_row)
    
    # 清除current_row之后的所有行的格式
    for row in range(current_row + 1, ws.max_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            cell.value = None
            cell.fill = PatternFill(fill_type=None)
            cell.font = Font(size=16)
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
    
    # 应用表格格式化，但不应用底色到空行
    apply_table_formatting(ws, header_row, apply_fill=False)


def sort_and_subtotal_by_project(target_wb):
    """对工资表汇总表按项目按项目分类排序并添加小计"""
    ws = target_wb['工资表汇总表按项目']
    
    # 找到表头行
    header_row = find_name_row(ws)
    if not header_row:
        return
        
    # 找到关键列的索引
    project_col = find_column_by_header(ws, header_row, '项目分类')
    if not project_col:
        return
        
    # 获取数据范围
    data_start_row = header_row + 2  # 跳过表头和日期行
    data_end_row = ws.max_row
    
    # 收集数据
    data = []
    for row in range(data_start_row, data_end_row + 1):
        row_data = []
        project = ws.cell(row=row, column=project_col).value
        if project and not ('合计' in str(project) or '小计' in str(project)):
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=row, column=col)
                row_data.append(cell.value)
            data.append(row_data)
    
    # 按项目分类排序（倒序）
    data.sort(key=lambda x: str(x[project_col-1]), reverse=True)
    
    # 清除所有数据行的内容和样式
    for row in range(data_start_row, data_end_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            cell.value = None
            # 清除单元格底色
            cell.fill = PatternFill(fill_type=None)
            # 重置字体为默认
            cell.font = Font(size=16)
            # 重置边框
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
    
    # 写回数据并添加小计
    current_row = data_start_row
    current_project = None
    project_start_row = current_row
    sequence_number = 1
    
    for row_data in data:
        project = row_data[project_col-1]
        
        # 如果项目分类变化，添加小计
        if current_project and project != current_project:
            # 添加小计行，不显示序号
            ws.cell(row=current_row, column=2, value=None)  # 序号留空
            ws.cell(row=current_row, column=project_col, value="小计")  # 只显示"小计"
            
            # 计算G到X列的小计
            for col in range(7, 25):  # G到X列
                sum_value = 0
                for r in range(project_start_row, current_row):
                    cell_value = ws.cell(row=r, column=col).value
                    if isinstance(cell_value, (int, float)):
                        sum_value += cell_value
                ws.cell(row=current_row, column=col, value=sum_value)
                ws.cell(row=current_row, column=col).number_format = '#,##0.00'
            
            # 设置小计行样式
            for col in range(1, ws.max_column + 1):
                cell = ws.cell(row=current_row, column=col)
                cell.font = Font(bold=True, size=16)  # 确保字体加粗
                cell.fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')  # 浅蓝色背景
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
            
            current_row += 1
            project_start_row = current_row
        
        # 写入数据行
        for col, value in enumerate(row_data, start=1):
            cell = ws.cell(row=current_row, column=col)
            if col == 1:  # 项目分类列保持原值
                cell.value = value
            elif col == 2:  # 序号放在B列
                cell.value = sequence_number
            else:
                cell.value = value
        
        current_project = project
        current_row += 1
        sequence_number += 1
    
    # 添加最后一个项目分类的小计
    if current_project:
        ws.cell(row=current_row, column=2, value=None)  # 序号留空
        ws.cell(row=current_row, column=project_col, value="小计")  # 只显示"小计"
        
        # 计算G到X列的小计
        for col in range(7, 25):  # G到X列
            sum_value = 0
            for r in range(project_start_row, current_row):
                cell_value = ws.cell(row=r, column=col).value
                if isinstance(cell_value, (int, float)):
                    sum_value += cell_value
                ws.cell(row=current_row, column=col, value=sum_value)
                ws.cell(row=current_row, column=col).number_format = '#,##0.00'
        
        # 设置小计行样式
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=current_row, column=col)
            cell.font = Font(bold=True, size=16)  # 确保字体加粗
            cell.fill = PatternFill(start_color='D9E1F2', end_color='D9E1F2', fill_type='solid')  # 浅蓝色背景
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
        
        current_row += 1
    
    # 添加总计行
    ws.cell(row=current_row, column=2, value=None)  # 序号留空
    ws.cell(row=current_row, column=project_col, value="总计")  # 总计放在项目分类列
    
    # 计算G到X列的总计
    for col in range(7, 25):  # G到X列
        sum_value = 0
        for r in range(data_start_row, current_row):
            cell_value = ws.cell(row=r, column=col).value
            if isinstance(cell_value, (int, float)) and not any(x in str(ws.cell(row=r, column=project_col).value) for x in ['小计', '总计']):
                sum_value += cell_value
        ws.cell(row=current_row, column=col, value=sum_value)
        ws.cell(row=current_row, column=col).number_format = '#,##0.00'
    
    # 处理总计行格式
    process_total_row(ws, current_row)
    
    # 记录最后一行有效数据的行号
    last_data_row = current_row
    
    # 清除current_row之后的所有行的格式
    for row in range(current_row + 1, ws.max_row + 1):
        for col in range(1, ws.max_column + 1):
            cell = ws.cell(row=row, column=col)
            cell.value = None
            cell.fill = PatternFill(fill_type=None)
            cell.font = Font(size=16)
            # 完全清除边框，而不是设置为细边框
            cell.border = Border(
                left=Side(style=None),
                right=Side(style=None),
                top=Side(style=None),
                bottom=Side(style=None)
            )
    
    # 应用表格格式化，但不应用底色到空行
    apply_table_formatting(ws, header_row, apply_fill=False)
    
    # 清除第三和第四行的底色（header_row + 2和header_row + 3）
    for row_idx in [header_row + 2, header_row + 3]:
        if row_idx <= ws.max_row:  # 确保行号不超出工作表范围
            for col_idx in range(1, ws.max_column + 1):
                cell = ws.cell(row=row_idx, column=col_idx)
                # 清除底色
                cell.fill = PatternFill(fill_type=None)


def copy_to_tax_import_sheet(target_wb):
    """将工资表汇总表1的数据复制到工资计税导入表，并处理税款调整数据"""
    # 获取源工作表
    source_ws = target_wb['工资表汇总表1']
    insurance_ws = target_wb['保险费']
    fund_ws = target_wb['公积金']
    target_ws = target_wb['工资计税导入表']
    tax_ws = target_wb['综合所得申报税款计算第一次']
    
    # 找到各表中的表头行
    source_header_row = find_name_row(source_ws)
    insurance_header_row = find_name_row(insurance_ws)
    fund_header_row = find_name_row(fund_ws)
    target_header_row = 1  # 直接指定工资计税导入表的表头行为第1行
    tax_header_row = find_name_row(tax_ws)
    
    # 检查关键工作表的表头行
    missing_headers = []
    if not source_header_row:
        missing_headers.append("工资表汇总表1")
    if not insurance_header_row:
        missing_headers.append("保险费")
    if not fund_header_row:
        missing_headers.append("公积金")

    # 对于税务表，如果为空则静默跳过税务相关处理
    # 只有当关键工作表缺失时才停止处理
    if missing_headers:
        # 静默返回，不输出错误信息
        return
    
    # 找到源表中的关键列
    source_cols = {
        '姓名': find_column_by_header(source_ws, source_header_row, '姓名'),
        '计税工资': find_column_by_header(source_ws, source_header_row, '计税工资'),
        '身份证号': find_column_by_header(source_ws, source_header_row, '身份证号'),
        '实发工资': find_column_by_header(source_ws, source_header_row, '实发工资'),  # 备用收入列
        '岗位工资': find_column_by_header(source_ws, source_header_row, '岗位工资'),  # 备用收入列
        '税款调整': 19,  # S列
        '公积金': 22     # V列
    }

    # 如果没有找到计税工资列，尝试使用其他收入列
    if not source_cols['计税工资']:
        if source_cols['实发工资']:
            source_cols['计税工资'] = source_cols['实发工资']
        elif source_cols['岗位工资']:
            source_cols['计税工资'] = source_cols['岗位工资']
    
    # 找到税款计算表中的关键列（如果税务表有数据的话）
    tax_cols = {}
    if tax_header_row:
        tax_cols = {
            '姓名': find_column_by_header(tax_ws, tax_header_row, '姓名'),
            '应补退税额': find_column_by_header(tax_ws, tax_header_row, '应补(退)税额')
        }
    else:
        tax_cols = {'姓名': None, '应补退税额': None}
    
    # 找到保险费表中的关键列
    insurance_cols = {
        '姓名': find_column_by_header(insurance_ws, insurance_header_row, '姓名'),
        '养老保险': 6,  # F列
        '医疗保险': 8,  # H列
        '失业保险': 11  # K列
    }
    
    # 找到公积金表中的关键列
    fund_cols = {
        '姓名': find_column_by_header(fund_ws, fund_header_row, '姓名'),
        '公积金': find_column_by_header(fund_ws, fund_header_row, '单位及个人分别缴纳')
    }
    
    # 找到目标表中的关键列（使用固定的表头行）
    target_cols = {
        '姓名': find_column_by_header(target_ws, target_header_row, '*姓名'),
        '证件类型': find_column_by_header(target_ws, target_header_row, '*证件类型'),
        '证件号码': find_column_by_header(target_ws, target_header_row, '*证件号码'),
        '本期收入': find_column_by_header(target_ws, target_header_row, '本期收入'),
        '基本养老保险费': find_column_by_header(target_ws, target_header_row, '基本养老保险费'),
        '基本医疗保险费': find_column_by_header(target_ws, target_header_row, '基本医疗保险费'),
        '失业保险费': find_column_by_header(target_ws, target_header_row, '失业保险费'),
        '住房公积金': find_column_by_header(target_ws, target_header_row, '住房公积金')
    }
    
    # 检查是否所有必要的列都找到了
    required_source_cols = ['姓名', '计税工资', '身份证号']
    required_target_cols = ['姓名', '证件类型', '证件号码', '本期收入', '基本养老保险费', 
                          '基本医疗保险费', '失业保险费', '住房公积金']
    
    if not all(source_cols[col] for col in required_source_cols):
        # 静默返回，不输出警告
        return

    if not all(target_cols[col] for col in required_target_cols):
        # 静默返回，不输出警告
        return
    
    # 创建税款调整数据映射
    tax_map = {}
    if tax_header_row and tax_cols['姓名'] and tax_cols['应补退税额']:
        for row in tax_ws.iter_rows(min_row=tax_header_row + 1):
            name = row[tax_cols['姓名'] - 1].value
            if name:
                tax_value = row[tax_cols['应补退税额'] - 1].value
                if isinstance(tax_value, (int, float)):
                    tax_map[name] = -abs(float(tax_value))  # 确保为负数
                else:
                    try:
                        numeric_value = float(tax_value) if tax_value else 0
                        tax_map[name] = -abs(numeric_value)  # 确保为负数
                    except (ValueError, TypeError):
                        tax_map[name] = 0
    
    # 创建保险费数据映射
    insurance_map = {}
    for row in insurance_ws.iter_rows(min_row=insurance_header_row + 1):
        name = row[insurance_cols['姓名'] - 1].value
        if name:
            insurance_map[name] = {
                '养老保险': row[insurance_cols['养老保险'] - 1].value,
                '医疗保险': row[insurance_cols['医疗保险'] - 1].value,
                '失业保险': row[insurance_cols['失业保险'] - 1].value
            }
    
    # 创建公积金数据映射
    fund_map = {}
    for row in fund_ws.iter_rows(min_row=fund_header_row + 1):
        name = row[fund_cols['姓名'] - 1].value
        if name:
            fund_map[name] = row[fund_cols['公积金'] - 1].value
    
    # 清除目标工作表的数据（保留表头）
    for row in target_ws.iter_rows(min_row=target_header_row + 1):
        for cell in row:
            cell.value = None
    
    # 填充数据
    target_row = target_header_row + 1  # 从表头的下一行开始填充数据

    # 动态确定数据起始行：从表头行开始向下查找第一个有姓名数据的行
    source_start_row = source_header_row + 1
    for row_idx in range(source_header_row + 1, source_ws.max_row + 1):
        name_cell = source_ws.cell(row=row_idx, column=source_cols['姓名'])
        if name_cell.value and str(name_cell.value).strip() and not any(x in str(name_cell.value) for x in ['姓名', '小计', '总计']):
            source_start_row = row_idx
            break



    # 获取源工作表中的所有数据
    source_data = []
    for row_idx in range(source_start_row, source_ws.max_row + 1):
        name_cell = source_ws.cell(row=row_idx, column=source_cols['姓名'])
        if name_cell.value and str(name_cell.value).strip():
            name = str(name_cell.value).strip()
            if not any(x in name for x in ['小计', '总计', '合计']):  # 排除小计和总计行
                row_data = []
                for col_idx in range(1, source_ws.max_column + 1):
                    row_data.append(source_ws.cell(row=row_idx, column=col_idx))
                source_data.append((name, row_data, row_idx))
    
    # 填充数据到目标工作表和源工作表的S列
    filled_count = 0
    for name, row_data, source_row_idx in source_data:
        # 填充姓名
        if target_cols['姓名']:
            target_ws.cell(row=target_row, column=target_cols['姓名'], value=name)

        # 填充证件类型
        if target_cols['证件类型']:
            target_ws.cell(row=target_row, column=target_cols['证件类型'], value='居民身份证')

        # 填充证件号码
        if target_cols['证件号码'] and source_cols['身份证号']:
            id_number = row_data[source_cols['身份证号'] - 1].value
            target_ws.cell(row=target_row, column=target_cols['证件号码'], value=id_number)

        # 填充本期收入（计税工资）
        if target_cols['本期收入'] and source_cols['计税工资']:
            salary_cell = row_data[source_cols['计税工资'] - 1]
            salary_value = 0
            if salary_cell.value is not None:
                try:
                    salary_value = float(salary_cell.value)
                except (ValueError, TypeError):
                    salary_value = 0
            target_ws.cell(row=target_row, column=target_cols['本期收入'], value=salary_value)
        
        # 填充保险费信息
        if name in insurance_map:
            # 基本养老保险费
            if target_cols['基本养老保险费']:
                pension_value = insurance_map[name]['养老保险']
                if isinstance(pension_value, (int, float)):
                    target_ws.cell(row=target_row, column=target_cols['基本养老保险费'], value=pension_value)
            
            # 基本医疗保险费
            if target_cols['基本医疗保险费']:
                medical_value = insurance_map[name]['医疗保险']
                if isinstance(medical_value, (int, float)):
                    target_ws.cell(row=target_row, column=target_cols['基本医疗保险费'], value=medical_value)
            
            # 失业保险费
            if target_cols['失业保险费']:
                unemployment_value = insurance_map[name]['失业保险']
                if isinstance(unemployment_value, (int, float)):
                    target_ws.cell(row=target_row, column=target_cols['失业保险费'], value=unemployment_value)
        
        # 填充公积金信息
        if target_cols['住房公积金'] and name in fund_map:
            fund_value = fund_map[name]
            if isinstance(fund_value, (int, float)):
                target_ws.cell(row=target_row, column=target_cols['住房公积金'], value=fund_value)
        
        # 填充公积金信息到S列（税款调整）- 修改这里，取负值
        if name in fund_map and not any(x in str(name) for x in ['小计', '总计']):
            fund_value = fund_map[name]
            if isinstance(fund_value, (int, float)):
                source_ws.cell(row=source_row_idx, column=source_cols['税款调整'], value=-abs(fund_value))  # 取负值
                source_ws.cell(row=source_row_idx, column=source_cols['税款调整']).number_format = '#,##0.00'

        target_row += 1
        filled_count += 1


    
    # 设置数字格式
    number_format_cols = ['本期收入', '基本养老保险费', '基本医疗保险费', '失业保险费', '住房公积金']
    for row in target_ws.iter_rows(min_row=target_header_row + 1):
        for col_name in number_format_cols:
            if target_cols[col_name]:
                cell = row[target_cols[col_name] - 1]
                if cell.value is not None:
                    cell.number_format = '#,##0.00'

    # 设置整个工作表的字体为10号并居中对齐
    from openpyxl.styles import Font, Alignment
    font_10 = Font(size=10)
    center_alignment = Alignment(horizontal='center', vertical='center')

    # 设置所有单元格的字体为10号并居中对齐
    for row in target_ws.iter_rows():
        for cell in row:
            cell.font = font_10
            cell.alignment = center_alignment


def associate_tax_adjustment(target_wb):
    """将"税额调整表"中的本月调整数据关联到"工资表汇总表1"中的补扣个税额列（T列）"""
    summary_ws = target_wb['工资表汇总表1']
    adjustment_ws = target_wb['税额调整表']

    summary_header_row = find_name_row(summary_ws)
    adjustment_header_row = find_name_row(adjustment_ws)

    if not all([summary_header_row, adjustment_header_row]):
        return

    # 获取列号（补扣个税额固定在T列，即第20列）
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '补扣个税额': 21  # U列
    }

    adjustment_cols = {
        '姓名': find_column_by_header(adjustment_ws, adjustment_header_row, '姓名'),
        '本月调整': find_column_by_header(adjustment_ws, adjustment_header_row, '本月调整')
    }

    if not all(summary_cols.values()) or not all(adjustment_cols.values()):
        return

    # 创建税额调整数据映射
    adjustment_map = {}
    for row in adjustment_ws.iter_rows(min_row=adjustment_header_row + 1):
        name = row[adjustment_cols['姓名'] - 1].value
        if name:
            adjustment_cell = row[adjustment_cols['本月调整'] - 1]
            value = adjustment_cell.value
            if isinstance(value, (int, float)):
                adjustment_map[name] = value
            else:
                try:
                    numeric_value = float(value) if value else 0
                    adjustment_map[name] = numeric_value
                except (ValueError, TypeError):
                    adjustment_map[name] = 0

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name in adjustment_map:
            target_cell = row[summary_cols['补扣个税额'] - 1]
            merged = is_merged_cell(summary_ws, target_cell.row, target_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)

            value = adjustment_map[name]
            target_cell.value = value
            target_cell.number_format = '#,##0.00'

            # 复制源单元格的样式
            source_cell = adjustment_ws.cell(row=row[0].row, column=adjustment_cols['本月调整'])
            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)


def associate_salary_adjustment(target_wb):
    """将"补扣工资"表中的金额数据关联到"工资表汇总表1"中的补扣工资列"""
    summary_ws = target_wb['工资表汇总表1']
    adjustment_ws = target_wb['补扣工资']

    summary_header_row = find_name_row(summary_ws)
    adjustment_header_row = find_name_row(adjustment_ws)

    if not all([summary_header_row, adjustment_header_row]):
        return

    # 获取列号
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '补扣工资': find_column_by_header(summary_ws, summary_header_row, '补扣工资')
    }

    adjustment_cols = {
        '姓名': find_column_by_header(adjustment_ws, adjustment_header_row, '姓名'),
        '金额': find_column_by_header(adjustment_ws, adjustment_header_row, '金额')
    }

    if not all(summary_cols.values()) or not all(adjustment_cols.values()):
        return

    # 创建补扣工资数据映射
    adjustment_map = {}
    for row in adjustment_ws.iter_rows(min_row=adjustment_header_row + 1):
        name = row[adjustment_cols['姓名'] - 1].value
        if name:
            adjustment_cell = row[adjustment_cols['金额'] - 1]
            value = adjustment_cell.value
            if isinstance(value, (int, float)):
                adjustment_map[name] = value
            else:
                try:
                    numeric_value = float(value) if value else 0
                    adjustment_map[name] = numeric_value
                except (ValueError, TypeError):
                    adjustment_map[name] = 0

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name in adjustment_map:
            target_cell = row[summary_cols['补扣工资'] - 1]
            merged = is_merged_cell(summary_ws, target_cell.row, target_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)

            value = adjustment_map[name]
            target_cell.value = value
            target_cell.number_format = '#,##0.00'

            # 复制源单元格的样式
            source_cell = adjustment_ws.cell(row=row[0].row, column=adjustment_cols['金额'])
            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)


def associate_others(target_wb):
    """将"其他"表中的金额数据关联到"工资表汇总表1"中的其他列"""
    summary_ws = target_wb['工资表汇总表1']
    others_ws = target_wb['其他']

    summary_header_row = find_name_row(summary_ws)
    others_header_row = find_name_row(others_ws)

    if not all([summary_header_row, others_header_row]):
        return

    # 获取列号
    summary_cols = {
        '姓名': find_column_by_header(summary_ws, summary_header_row, '姓名'),
        '其他': find_column_by_header(summary_ws, summary_header_row, '其他')
    }

    others_cols = {
        '姓名': find_column_by_header(others_ws, others_header_row, '姓名'),
        '金额': find_column_by_header(others_ws, others_header_row, '金额')
    }

    if not all(summary_cols.values()) or not all(others_cols.values()):
        return

    # 创建其他数据映射
    others_map = {}
    for row in others_ws.iter_rows(min_row=others_header_row + 1):
        name = row[others_cols['姓名'] - 1].value
        if name:
            others_cell = row[others_cols['金额'] - 1]
            value = others_cell.value
            if isinstance(value, (int, float)):
                others_map[name] = value
            else:
                try:
                    numeric_value = float(value) if value else 0
                    others_map[name] = numeric_value
                except (ValueError, TypeError):
                    others_map[name] = 0

    # 填充数据到汇总表
    start_row = 5  # 从第5行开始填充数据
    for row in summary_ws.iter_rows(min_row=start_row):
        name = row[summary_cols['姓名'] - 1].value
        if name in others_map:
            target_cell = row[summary_cols['其他'] - 1]
            merged = is_merged_cell(summary_ws, target_cell.row, target_cell.column)
            if merged:
                target_row, target_col = merged
                target_cell = summary_ws.cell(row=target_row, column=target_col)

            value = others_map[name]
            target_cell.value = value
            target_cell.number_format = '#,##0.00'

            # 复制源单元格的样式
            source_cell = others_ws.cell(row=row[0].row, column=others_cols['金额'])
            if source_cell.has_style:
                target_cell.font = copy(source_cell.font)
                target_cell.border = copy(source_cell.border)
                target_cell.fill = copy(source_cell.fill)
                target_cell.protection = copy(source_cell.protection)
                target_cell.alignment = copy(source_cell.alignment)


def calculate_salary_summary(target_wb):
    """计算工资表汇总表1中O列和P列的值
    O列 = G列 + H列 + J列 + K列 + L列 + M列 + N列
    P列 = O列 - J列
    """
    summary_ws = target_wb['工资表汇总表1']
    header_row = find_name_row(summary_ws)

    if not header_row:
        return

    # 获取列号
    cols = {
        'G': 7,   # G列
        'H': 8,   # H列
        'I': 9,   # I列
        'J': 10,  # J列
        'K': 11,  # K列
        'L': 12,  # L列
        'M': 13,  # M列
        'N': 14,  # N列
        'O': 15,  # O列
        'P': 16   # P列
    }

    # 从第5行开始计算
    start_row = 5
    for row in summary_ws.iter_rows(min_row=start_row):
        # 获取J列的值（用于计算P列）
        j_value = 0
        j_cell = row[cols['J'] - 1]
        if j_cell.value is not None:
            try:
                j_value = float(j_cell.value) if j_cell.value else 0
            except (ValueError, TypeError):
                j_value = 0

        # 计算O列的值（G+H+J+K+L+M+N的和）
        total = 0
        for col_letter in ['G', 'H', 'J', 'K', 'L', 'M', 'N']:
            cell = row[cols[col_letter] - 1]
            if cell.value is not None:
                try:
                    value = float(cell.value) if cell.value else 0
                    total += value
                except (ValueError, TypeError):
                    pass

        # 更新O列
        o_cell = row[cols['O'] - 1]
        o_cell.value = total
        o_cell.number_format = '#,##0.00'

        # 更新P列（O列减去J列）
        p_cell = row[cols['P'] - 1]
        p_cell.value = total - j_value
        p_cell.number_format = '#,##0.00'


def range_boundaries(range_string):
    """解析Excel单元格范围字符串，返回最小行、最小列、最大行、最大列"""
    # 处理单个单元格的情况
    if ':' not in range_string:
        col = ''.join(c for c in range_string if c.isalpha())
        row = int(''.join(c for c in range_string if c.isdigit()))
        return row, column_index_from_string(col), row, column_index_from_string(col)
    
    # 处理范围的情况
    start, end = range_string.split(':')
    
    # 解析起始单元格
    start_col = ''.join(c for c in start if c.isalpha())
    start_row = int(''.join(c for c in start if c.isdigit()))
    
    # 解析结束单元格
    end_col = ''.join(c for c in end if c.isalpha())
    end_row = int(''.join(c for c in end if c.isdigit()))
    
    return (start_row, column_index_from_string(start_col),
            end_row, column_index_from_string(end_col))


def calculate_performance_import_data(target_wb):
    """计算工资表汇总表1中的绩效工资导入数据（I列）
    计算公式：I列 = H列 + J列 + K列 + L列 + Q列 + R列
    """
    summary_ws = target_wb['工资表汇总表1']
    header_row = find_name_row(summary_ws)

    if not header_row:
        return

    # 获取列号
    cols = {
        'H': 8,   # 绩效工资
        'I': 9,   # 绩效工资导入数据
        'J': 10,  # 独生子女费
        'K': 11,  # 护龄津贴
        'L': 12,  # 周六日节假日加班补助
        'Q': 17,  # 房租
        'R': 18   # 水电费
    }

    # 从第5行开始计算
    start_row = 5
    for row in summary_ws.iter_rows(min_row=start_row):
        total = 0
        
        # 计算所有列的和
        for col_letter in ['H', 'J', 'K', 'L', 'Q', 'R']:
            cell = row[cols[col_letter] - 1]
            if cell.value is not None:
                try:
                    value = float(cell.value) if cell.value else 0
                    total += value
                except (ValueError, TypeError):
                    pass

        # 更新I列（绩效工资导入数据）
        i_cell = row[cols['I'] - 1]
        i_cell.value = total
        i_cell.number_format = '#,##0.00'


def apply_table_formatting(ws, header_row, apply_fill=True):
    """应用表格格式化：设置行高、字体大小、边框等
    
    Args:
        ws: 工作表对象
        header_row: 表头行号
        apply_fill: 是否应用单元格底色，默认为True
    """
    # 设置工作表缩放比例为70%
    ws.sheet_view.zoomScale = 70
    
    # 设置第一行（表头）格式
    ws.row_dimensions[1].height = 50  # 设置第一行行高
    for cell in ws[1]:
        if not isinstance(cell, openpyxl.cell.cell.MergedCell):
            cell.font = Font(size=26, bold=True)
            cell.alignment = Alignment(horizontal='center', vertical='center')
            cell.border = Border(
                left=Side(style='thin'),
                right=Side(style='thin'),
                top=Side(style='thin'),
                bottom=Side(style='thin')
            )
    
    # 设置第二行日期的格式（左对齐）和行高
    ws.row_dimensions[2].height = 30  # 设置第二行行高
    date_cell = ws.cell(row=2, column=1)
    date_cell.font = Font(size=16)
    date_cell.alignment = Alignment(horizontal='left', vertical='center')
    date_cell.border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 设置第三行和第四行格式（加粗）和行高
    for row in [3, 4]:
        ws.row_dimensions[row].height = 30  # 设置第三、四行行高
        for cell in ws[row]:
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                cell.font = Font(size=16, bold=True)
                cell.alignment = Alignment(horizontal='center', vertical='center')
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )
    
    # 设置所有单元格的基本格式
    for row in ws.iter_rows(min_row=5):  # 从第五行开始
        # 检查行是否有内容
        row_has_content = False
        for cell in row:
            if cell.value is not None:
                row_has_content = True
                break
                
        for cell in row:
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                # 设置字体大小为16
                cell.font = Font(size=16)
                # 居中对齐
                cell.alignment = Alignment(horizontal='center', vertical='center')
                
                # 只对有内容的行应用边框
                if row_has_content:
                    # 添加边框
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )
                else:
                    # 对没有内容的行清除边框
                    cell.border = Border(
                        left=Side(style=None),
                        right=Side(style=None),
                        top=Side(style=None),
                        bottom=Side(style=None)
                    )
                
                # 只对有内容的行设置底色
                if apply_fill and row_has_content:
                    # 如果单元格值包含'合计'或'小计'，则不在这里设置底色，因为这些行在其他地方已经设置了特殊样式
                    if not (cell.value and ('合计' in str(cell.value) or '小计' in str(cell.value) or '总计' in str(cell.value))):
                        pass  # 底色在其他地方设置
    
    # 从第5行开始设置行高为30，但只对有内容的行
    for row in range(5, ws.max_row + 1):
        # 检查行是否有内容
        row_has_content = False
        for col in range(1, ws.max_column + 1):
            if ws.cell(row=row, column=col).value is not None:
                row_has_content = True
                break
                
        if row_has_content:
            ws.row_dimensions[row].height = 30
    
    # 设置第一列的列宽为57
    ws.column_dimensions['A'].width = 57
    
    # 自动调整其他列宽以完全显示内容
    for col_idx in range(2, ws.max_column + 1):  # 从第二列开始
        max_length = 0
        column_letter = get_column_letter(col_idx)
        
        # 遍历该列的所有单元格
        for row in ws.iter_rows():
            cell = row[col_idx - 1]
            if not isinstance(cell, openpyxl.cell.cell.MergedCell):
                try:
                    if cell.value:
                        # 考虑字体大小对宽度的影响
                        font_size_factor = cell.font.size / 11 if hasattr(cell.font, 'size') and cell.font.size else 1
                        
                        # 对于数字类型，转换为格式化后的字符串计算长度
                        if isinstance(cell.value, (int, float)):
                            cell_text = str('{:,.2f}'.format(cell.value))
                        else:
                            cell_text = str(cell.value)
                        
                        # 计算字符串的实际显示长度（中文字符计为2个单位）
                        cell_length = sum(2 if '\u4e00' <= char <= '\u9fff' else 1 for char in cell_text)
                        
                        # 应用字体大小因子
                        cell_length = cell_length * font_size_factor
                        
                        if cell_length > max_length:
                            max_length = cell_length
                except:
                    pass
        
        # 设置列宽（考虑到中文字符和一些额外的padding）
        if max_length > 0:
            adjusted_width = max_length + 4  # 添加一些padding
            ws.column_dimensions[column_letter].width = adjusted_width


def ensure_number_format(ws, start_row=5):
    """确保所有数值单元格使用正确的格式"""
    # 需要检查的列（使用列号，从1开始）
    numeric_cols = {
        7: '#,##0.00',   # G列 岗位工资
        8: '#,##0.00',   # H列 绩效工资
        9: '#,##0.00',   # I列 绩效工资导入数据
        10: '#,##0.00',  # J列 独生子女费
        11: '#,##0.00',  # K列 护龄津贴
        12: '#,##0.00',  # L列 周六日节假日加班补助
        13: '#,##0.00',  # M列 补扣工资
        14: '#,##0.00',  # N列 其他
        15: '#,##0.00',  # O列 应付工资
        16: '#,##0.00',  # P列 计税工资
        17: '#,##0.00',  # Q列 房租
        18: '#,##0.00',  # R列 水电费
        19: '#,##0.00',  # S列 公积金
        20: '#,##0.00',  # T列 个税
        21: '#,##0.00',  # U列 补扣个税额
        22: '#,##0.00',  # V列 工会会费
        23: '#,##0.00',  # W列 保险费
        24: '#,##0.00'   # X列 实发工资
    }

    for row in ws.iter_rows(min_row=start_row):
        for col, number_format in numeric_cols.items():
            cell = row[col - 1]
            if cell.value is not None:
                try:
                    # 如果是字符串，尝试转换为浮点数
                    if isinstance(cell.value, str):
                        # 移除所有非数字字符（保留小数点和负号）
                        value_str = ''.join(c for c in cell.value if c.isdigit() or c in '.-')
                        if value_str:
                            cell.value = float(value_str)
                    # 如果是数字，确保是浮点数
                    elif isinstance(cell.value, (int, float)):
                        cell.value = float(cell.value)
                    
                    # 设置数字格式（确保所有数字单元格都使用统一的货币格式）
                    cell.number_format = number_format
                except (ValueError, TypeError):
                    pass

def format_summary_tables(target_wb):
    """格式化工资表汇总表1和工资表汇总表按项目的样式"""
    # 处理工资表汇总表按项目
    if '工资表汇总表按项目' in target_wb.sheetnames:
        project_summary_ws = target_wb['工资表汇总表按项目']
        
        # 确保J、K、L、M、N列的所有数据都显示为两位小数
        for col_letter in ['J', 'K', 'L', 'M', 'N']:
            col_idx = column_index_from_string(col_letter)
            for row in project_summary_ws.iter_rows(min_row=5):  # 从第5行开始处理数据
                cell = row[col_idx - 1]
                if isinstance(cell.value, (int, float)) or (isinstance(cell.value, str) and cell.value and cell.value.replace('.', '', 1).replace('-', '', 1).isdigit()):
                    # 如果是字符串形式的数字，转换为浮点数
                    if isinstance(cell.value, str):
                        try:
                            cell.value = float(cell.value)
                        except ValueError:
                            continue
                    # 设置为带千分位和两位小数的格式
                    cell.number_format = '#,##0.00'

def set_zero_cells_to_empty(wb):
    """将工资表汇总表1和工资表汇总表按项目中的0值单元格设置为空"""
    # 处理的工作表
    sheets = ['工资表汇总表1', '工资表汇总表按项目']
    
    for sheet_name in sheets:
        if sheet_name in wb.sheetnames:
            ws = wb[sheet_name]
            
            # 找到表头行
            header_row = find_name_row(ws)
            if not header_row:
                continue
                
            # 需要检查的列（使用列号，从1开始）
            numeric_cols = [
                7, 8, 9, 10, 11, 12, 13, 14,  # G到N列
                17, 18, 19, 20, 21, 22, 23     # Q到W列
            ]
            
            # 从第5行开始处理数据
            for row in ws.iter_rows(min_row=5):
                for col_idx in numeric_cols:
                    if col_idx <= len(row):
                        cell = row[col_idx - 1]
                        # 检查单元格是否为0或0.00
                        if isinstance(cell.value, (int, float)) and abs(cell.value) < 0.01:
                            cell.value = None


def safe_load_workbook(filename, data_only=False, silent=True):
    """安全加载工作簿，包含错误处理和修复尝试"""
    import os

    try:
        if not silent:
            print(f"尝试加载文件: {filename}")
        wb = openpyxl.load_workbook(filename, data_only=data_only)
        if not silent:
            print(f"成功加载文件: {filename}")
        return wb
    except Exception as e:
        if not silent:
            print(f"加载文件 {filename} 失败: {str(e)}")

        # 尝试不同的加载方式
        try:
            if not silent:
                print(f"尝试以只读模式加载: {filename}")
            wb = openpyxl.load_workbook(filename, read_only=True, data_only=data_only)
            if not silent:
                print(f"只读模式加载成功，但需要转换为可写模式...")

            # 创建新的工作簿并复制数据
            new_wb = openpyxl.Workbook()
            new_wb.remove(new_wb.active)  # 删除默认工作表

            for sheet_name in wb.sheetnames:
                source_sheet = wb[sheet_name]
                new_sheet = new_wb.create_sheet(sheet_name)

                # 复制数据
                for row in source_sheet.iter_rows(values_only=True):
                    new_sheet.append(row)

            wb.close()
            if not silent:
                print(f"成功转换为可写模式: {filename}")
            return new_wb

        except Exception as e2:
            if not silent:
                print(f"只读模式也失败: {str(e2)}")

            # 检查是否有备用文件
            backup_files = [
                f"模版/{filename.replace('.xlsx', ' - 模版.xlsx')}",
                f"历史文档/{filename}",
                f"表格/{filename}"
            ]

            for backup_file in backup_files:
                if os.path.exists(backup_file):
                    try:
                        if not silent:
                            print(f"尝试加载备用文件: {backup_file}")
                        wb = openpyxl.load_workbook(backup_file, data_only=data_only)
                        if not silent:
                            print(f"成功加载备用文件: {backup_file}")
                        return wb
                    except:
                        continue

            raise FileNotFoundError(f"无法加载文件 {filename} 及其备用文件。请检查文件是否损坏或格式是否正确。")

def main():
    try:
        # 初始化变量
        print("正在加载文件...")
        source_wb = safe_load_workbook('科室绩效汇总.xlsx', silent=True)
        if not source_wb:
            print("错误: 无法加载科室绩效汇总.xlsx文件")
            return

        target_wb = None
        updated_wb = None
        target_file = '聘用人员工资表_新.xlsx'

        # 定义所有步骤
        steps = [
            ("处理科室绩效汇总文件", lambda: update_summary_sheet(source_wb)),
            ("打开目标文件", lambda: safe_load_workbook('聘用人员工资表.xlsx', data_only=True, silent=True)),
            ("复制绩效工资数据", lambda: copy_sheet(source_wb, '汇总表', target_wb, '绩效工资')),
            ("保存中间文件", lambda: safe_save_workbook(target_wb, target_file, silent=True)),
            ("重新打开文件进行信息填充", lambda: safe_load_workbook(target_file, data_only=True, silent=True)),
            ("填充当天日期", lambda: fill_today_date(updated_wb)),
            ("填充员工基本信息", lambda: fill_employee_info(updated_wb)),
            ("关联绩效工资", lambda: associate_performance_salary(updated_wb)),
            ("关联项目支出分类信息", lambda: associate_project_info(updated_wb)),
            ("关联补贴信息", lambda: associate_additional_salary(updated_wb)),
            ("关联房租和水电信息", lambda: associate_rent(updated_wb)),
            ("关联公积金信息", lambda: associate_housing_fund(updated_wb)),
            ("关联工会会费信息", lambda: associate_union_fees(updated_wb)),
            ("关联保险费信息", lambda: associate_insurance_fees(updated_wb)),
            ("关联个税信息", lambda: associate_tax(updated_wb)),
            ("关联补扣个税额信息", lambda: associate_tax_adjustment(updated_wb)),
            ("关联补扣工资信息", lambda: associate_salary_adjustment(updated_wb)),
            ("关联其他信息", lambda: associate_others(updated_wb)),
            ("计算绩效工资导入数据", lambda: calculate_performance_import_data(updated_wb)),
            ("计算工资汇总", lambda: calculate_salary_summary(updated_wb)),
            ("计算实发工资", lambda: calculate_net_salary(updated_wb)),
            ("对工资表汇总表1进行排序和小计", lambda: sort_and_subtotal_by_department(updated_wb)),
            ("确保数字格式统一", lambda: ensure_number_format(updated_wb['工资表汇总表1'])),
            ("将0值单元格设置为空", lambda: set_zero_cells_to_empty(updated_wb)),  # 新增步骤
            ("复制数据到工资表汇总表按项目", lambda: copy_to_project_summary(updated_wb)),
            ("对工资表汇总表按项目进行排序和小计", lambda: sort_and_subtotal_by_project(updated_wb)),
            ("格式化工资表汇总表", lambda: format_summary_tables(updated_wb)),
            ("复制数据到工资计税导入表", lambda: copy_to_tax_import_sheet(updated_wb)),
            ("冻结工资表汇总表1的第四行", lambda: setattr(updated_wb['工资表汇总表1'], 'freeze_panes', 'A5')),
            ("冻结工资表汇总表按项目的第四行", lambda: setattr(updated_wb['工资表汇总表按项目'], 'freeze_panes', 'A5')),
            ("保存最终文件", lambda: safe_save_workbook(updated_wb, target_file, silent=True))
        ]
        
        # 显示进度条
        print("开始处理工资表...")
        for i, (step_name, step_func) in enumerate(steps):
            # 在执行步骤前显示进度
            percentage = int((i + 1) / len(steps) * 100)
            bar_length = 40
            filled_length = int(bar_length * (i + 1) // len(steps))
            bar = '█' * filled_length + '░' * (bar_length - filled_length)
            print(f'\r处理进度: {percentage:3d}%|{bar}| {i+1}/{len(steps)}', end='', flush=True)

            try:
                # 执行步骤
                result = step_func()

                # 根据步骤更新变量
                if step_name == "打开目标文件":
                    target_wb = result
                    if not target_wb:
                        raise FileNotFoundError("无法加载聘用人员工资表.xlsx文件")
                elif step_name == "重新打开文件进行信息填充":
                    updated_wb = result
                    if not updated_wb:
                        raise FileNotFoundError(f"无法加载{target_file}文件")

            except Exception as e:
                # 静默处理错误，只显示进度条中断
                print(f"\n处理中断: {step_name}")
                raise

        print("\n处理完成!")

    except FileNotFoundError as e:
        print(f"\n错误: 文件未找到")
    except Exception as e:
        print(f"\n错误: 处理失败")


if __name__ == '__main__':
    main()