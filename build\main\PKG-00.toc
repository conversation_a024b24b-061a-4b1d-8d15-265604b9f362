('D:\\工资报表\\build\\main\\main.pkg',
 {'BINARY': True,
  'DATA': True,
  'EXECUTABLE': True,
  'EXTENSION': True,
  'PYMODULE': True,
  'PYSOURCE': True,
  'PYZ': False,
  'SPLASH': True,
  'SYMLINK': False},
 [('pyi-contents-directory _internal', '', 'OPTION'),
  ('PYZ-00.pyz', 'D:\\工资报表\\build\\main\\PYZ-00.pyz', 'PYZ'),
  ('struct', 'D:\\工资报表\\build\\main\\localpycs\\struct.pyc', 'PYMODULE'),
  ('pyimod01_archive',
   'D:\\工资报表\\build\\main\\localpycs\\pyimod01_archive.pyc',
   'PYMODULE'),
  ('pyimod02_importers',
   'D:\\工资报表\\build\\main\\localpycs\\pyimod02_importers.pyc',
   'PYMODULE'),
  ('pyimod03_ctypes',
   'D:\\工资报表\\build\\main\\localpycs\\pyimod03_ctypes.pyc',
   'PYMODULE'),
  ('pyimod04_pywin32',
   'D:\\工资报表\\build\\main\\localpycs\\pyimod04_pywin32.pyc',
   'PYMODULE'),
  ('pyiboot01_bootstrap',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\loader\\pyiboot01_bootstrap.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main', 'D:\\工资报表\\main.py', 'PYSOURCE'),
  ('libiomp5md.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libiomp5md.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('libexpat.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('vcruntime140_1.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('tcl86t.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('mkl_def.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_def.2.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('msvcp140_2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_2.dll',
   'BINARY'),
  ('mkl_vml_avx2.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_avx2.2.dll',
   'BINARY'),
  ('mkl_vml_mc.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_mc.2.dll',
   'BINARY'),
  ('mkl_blacs_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_lp64.2.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_intelmpi_lp64.2.dll',
   'BINARY'),
  ('tbb12.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tbb12.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_msmpi_ilp64.2.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('omptarget.rtl.opencl.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.rtl.opencl.dll',
   'BINARY'),
  ('tbbmalloc.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tbbmalloc.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('bzip2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\bzip2.dll',
   'BINARY'),
  ('mkl_blacs_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_ilp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('tk86t.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('omptarget.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('vccorlib140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vccorlib140.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('vcomp140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcomp140.dll',
   'BINARY'),
  ('ffi.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_intelmpi_ilp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('mkl_mc3.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_mc3.2.dll',
   'BINARY'),
  ('mkl_scalapack_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_scalapack_ilp64.2.dll',
   'BINARY'),
  ('msvcp140_atomic_wait.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_atomic_wait.dll',
   'BINARY'),
  ('tbbmalloc_proxy.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tbbmalloc_proxy.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-console-l1-2-0.dll',
   'BINARY'),
  ('ffi-7.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ffi-7.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ucrtbase.dll',
   'BINARY'),
  ('vcruntime140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcruntime140.dll',
   'BINARY'),
  ('mkl_avx2.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_avx2.2.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_msmpi_lp64.2.dll',
   'BINARY'),
  ('zlib.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\zlib.dll',
   'BINARY'),
  ('mkl_msg.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_msg.dll',
   'BINARY'),
  ('mkl_tbb_thread.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_tbb_thread.2.dll',
   'BINARY'),
  ('mkl_vml_mc3.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_mc3.2.dll',
   'BINARY'),
  ('vcruntime140_threads.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcruntime140_threads.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('mkl_avx512.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_avx512.2.dll',
   'BINARY'),
  ('mkl_mc.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_mc.2.dll',
   'BINARY'),
  ('mkl_intel_thread.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_intel_thread.2.dll',
   'BINARY'),
  ('vcamp140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcamp140.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_avx.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_avx.2.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('omptarget.sycl.wrap.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.sycl.wrap.dll',
   'BINARY'),
  ('mkl_cdft_core.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_cdft_core.2.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('mkl_scalapack_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_scalapack_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('ffi-8.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('msvcp140_codecvt_ids.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_codecvt_ids.dll',
   'BINARY'),
  ('libbz2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libbz2.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('mkl_avx.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_avx.2.dll',
   'BINARY'),
  ('concrt140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\concrt140.dll',
   'BINARY'),
  ('mkl_rt.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_rt.2.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('msvcp140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140.dll',
   'BINARY'),
  ('libimalloc.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libimalloc.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_cmpt.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_cmpt.2.dll',
   'BINARY'),
  ('mkl_vml_def.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_def.2.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('libiompstubs5md.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libiompstubs5md.dll',
   'BINARY'),
  ('mkl_core.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_core.2.dll',
   'BINARY'),
  ('omptarget.rtl.level0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.rtl.level0.dll',
   'BINARY'),
  ('mkl_sequential.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_sequential.2.dll',
   'BINARY'),
  ('mkl_vml_avx512.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_avx512.2.dll',
   'BINARY'),
  ('msvcp140_1.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_1.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('mkl_pgi_thread.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_pgi_thread.2.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('python311.dll', 'D:\\Users\\ZhangLan\\miniconda3\\python311.dll', 'BINARY'),
  ('libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('unicodedata.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Users\\ZhangLan\\miniconda3\\python3.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('libwebpdemux.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libwebpdemux.dll',
   'BINARY'),
  ('libwebp.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libwebp.dll',
   'BINARY'),
  ('libwebpmux.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libwebpmux.dll',
   'BINARY'),
  ('lcms2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\lcms2.dll',
   'BINARY'),
  ('openjp2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\openjp2.dll',
   'BINARY'),
  ('tiff.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tiff.dll',
   'BINARY'),
  ('libsharpyuv.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libsharpyuv.dll',
   'BINARY'),
  ('deflate.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\deflate.dll',
   'BINARY'),
  ('zstd.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\zstd.dll',
   'BINARY'),
  ('Lerc.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\Lerc.dll',
   'BINARY'),
  ('base_library.zip', 'D:\\工资报表\\build\\main\\base_library.zip', 'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\direct_url.json',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\direct_url.json',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\RECORD',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\top_level.txt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\WHEEL',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\INSTALLER',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\METADATA',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\REQUESTED',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\LICENSE',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\LICENSE',
   'DATA'),
  ('wheel-0.43.0.dist-info\\METADATA',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info\\INSTALLER',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.43.0.dist-info\\LICENSE.txt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\entry_points.txt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\RECORD',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\RECORD',
   'DATA'),
  ('wheel-0.43.0.dist-info\\WHEEL',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA')],
 'python311.dll',
 False,
 False,
 False,
 [],
 None,
 None,
 None)
