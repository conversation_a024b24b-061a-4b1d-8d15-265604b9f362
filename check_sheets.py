#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import openpyxl

def check_sheets():
    try:
        print("检查聘用人员工资表.xlsx中的工作表...")
        wb = openpyxl.load_workbook('聘用人员工资表.xlsx')
        print("工作表列表:")
        for i, sheet_name in enumerate(wb.sheetnames, 1):
            print(f"{i}. {sheet_name}")
        wb.close()
        
        print("\n检查聘用人员工资表_新.xlsx中的工作表...")
        wb_new = openpyxl.load_workbook('聘用人员工资表_新.xlsx')
        print("工作表列表:")
        for i, sheet_name in enumerate(wb_new.sheetnames, 1):
            print(f"{i}. {sheet_name}")
        wb_new.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    check_sheets()
