#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import openpyxl

def debug_workbook():
    try:
        print("正在检查原始文件...")
        wb = openpyxl.load_workbook('聘用人员工资表.xlsx', data_only=True)
        print("原始文件工作表:")
        for i, sheet_name in enumerate(wb.sheetnames, 1):
            print(f"  {i}. {sheet_name}")
        
        # 检查是否有岗位工资工作表
        print("\n检查工作表名称详情:")
        for sheet_name in wb.sheetnames:
            print(f"  '{sheet_name}' (长度: {len(sheet_name)}, repr: {repr(sheet_name)})")
            if '岗位工资' in sheet_name or sheet_name.strip() == '岗位工资':
                print(f"    ✓ 找到岗位工资相关工作表: '{sheet_name}'")
                try:
                    position_ws = wb[sheet_name]
                    print(f"      行数: {position_ws.max_row}")
                    print(f"      列数: {position_ws.max_column}")
                except Exception as e:
                    print(f"      访问失败: {e}")
        
        wb.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    debug_workbook()
