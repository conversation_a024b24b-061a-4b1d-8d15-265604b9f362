(['D:\\salary_reports\\main.py'],
 ['D:\\salary_reports'],
 ['openpyxl.cell._writer'],
 ['D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_pyinstaller',
  'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks\\stdhooks',
  'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\_pyinstaller_hooks_contrib\\hooks'],
 {},
 [],
 [],
 False,
 {},
 0,
 [],
 [],
 '3.11.9 | packaged by conda-forge | (main, Apr 19 2024, 18:27:10) [MSC v.1938 '
 '64 bit (AMD64)]',
 [('pyi_rth_pkgutil',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_inspect',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_pkgres',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgres.py',
   'PYSOURCE'),
  ('pyi_rth_setuptools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_setuptools.py',
   'PYSOURCE'),
  ('main', 'D:\\salary_reports\\main.py', 'PYSOURCE')],
 [('_distutils_hack',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\_distutils_hack\\__init__.py',
   'PYMODULE'),
  ('importlib.util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('threading',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\threading.py',
   'PYMODULE'),
  ('_threading_local',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('contextlib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\contextlib.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('urllib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\urllib\\__init__.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('random', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\random.py', 'PYMODULE'),
  ('statistics',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\statistics.py',
   'PYMODULE'),
  ('decimal', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_pydecimal.py',
   'PYMODULE'),
  ('contextvars',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('fractions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\fractions.py',
   'PYMODULE'),
  ('numbers', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('logging',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\logging\\__init__.py',
   'PYMODULE'),
  ('pickle', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('argparse', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('gettext', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('token', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\token.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('dis', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('struct', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\struct.py', 'PYMODULE'),
  ('bisect', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('base64', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\base64.py', 'PYMODULE'),
  ('getopt', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('email.charset',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('urllib.parse',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\urllib\\parse.py',
   'PYMODULE'),
  ('ipaddress',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ipaddress.py',
   'PYMODULE'),
  ('socket', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\socket.py', 'PYMODULE'),
  ('selectors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\selectors.py',
   'PYMODULE'),
  ('quopri', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('textwrap', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('zipfile', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\py_compile.py',
   'PYMODULE'),
  ('lzma', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('_compression',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('bz2', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('pathlib', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pathlib.py', 'PYMODULE'),
  ('fnmatch', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\fnmatch.py', 'PYMODULE'),
  ('email',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\__init__.py',
   'PYMODULE'),
  ('email.parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('tokenize', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tokenize.py', 'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.abc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('setuptools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.zosccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\zosccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.versionpredicate',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\versionpredicate.py',
   'PYMODULE'),
  ('setuptools._distutils.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\version.py',
   'PYMODULE'),
  ('setuptools._distutils.util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\util.py',
   'PYMODULE'),
  ('distutils.sysconfig',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\sysconfig.py',
   'PYMODULE'),
  ('_osx_support',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_osx_support.py',
   'PYMODULE'),
  ('distutils.text_file',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\text_file.py',
   'PYMODULE'),
  ('distutils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\__init__.py',
   'PYMODULE'),
  ('distutils.archive_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\archive_util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('gzip', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('distutils.dir_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\dir_util.py',
   'PYMODULE'),
  ('distutils.file_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\file_util.py',
   'PYMODULE'),
  ('distutils.dep_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\dep_util.py',
   'PYMODULE'),
  ('distutils.spawn',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\spawn.py',
   'PYMODULE'),
  ('distutils.debug',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\debug.py',
   'PYMODULE'),
  ('distutils.log',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\log.py',
   'PYMODULE'),
  ('sysconfig',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\sysconfig.py',
   'PYMODULE'),
  ('_aix_support',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('subprocess',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\subprocess.py',
   'PYMODULE'),
  ('signal', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\signal.py', 'PYMODULE'),
  ('setuptools._distutils.unixccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\unixccompiler.py',
   'PYMODULE'),
  ('shlex', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('__future__',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\__future__.py',
   'PYMODULE'),
  ('setuptools._distutils.text_file',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\text_file.py',
   'PYMODULE'),
  ('setuptools._distutils.sysconfig',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\sysconfig.py',
   'PYMODULE'),
  ('setuptools._distutils.spawn',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\spawn.py',
   'PYMODULE'),
  ('setuptools._distutils.py39compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\py39compat.py',
   'PYMODULE'),
  ('platform', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\platform.py', 'PYMODULE'),
  ('setuptools._distutils.py38compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\py38compat.py',
   'PYMODULE'),
  ('setuptools._distutils.msvccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvccompiler.py',
   'PYMODULE'),
  ('distutils.msvc9compiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('distutils.ccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\ccompiler.py',
   'PYMODULE'),
  ('distutils.fancy_getopt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('win32con',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('setuptools._distutils.msvc9compiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\msvc9compiler.py',
   'PYMODULE'),
  ('setuptools._distutils.log',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\log.py',
   'PYMODULE'),
  ('setuptools._distutils.filelist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools._distutils.file_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\file_util.py',
   'PYMODULE'),
  ('setuptools._distutils.fancy_getopt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\fancy_getopt.py',
   'PYMODULE'),
  ('setuptools._distutils.extension',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\extension.py',
   'PYMODULE'),
  ('setuptools._distutils.errors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\errors.py',
   'PYMODULE'),
  ('setuptools._distutils.dist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\dist.py',
   'PYMODULE'),
  ('distutils.versionpredicate',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\versionpredicate.py',
   'PYMODULE'),
  ('distutils.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\version.py',
   'PYMODULE'),
  ('distutils.command',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.cmd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\cmd.py',
   'PYMODULE'),
  ('distutils.dist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\dist.py',
   'PYMODULE'),
  ('configparser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\configparser.py',
   'PYMODULE'),
  ('setuptools._distutils.dir_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\dir_util.py',
   'PYMODULE'),
  ('setuptools._distutils.dep_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\dep_util.py',
   'PYMODULE'),
  ('setuptools._distutils.debug',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\debug.py',
   'PYMODULE'),
  ('setuptools._distutils.cygwinccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\cygwinccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.core',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\core.py',
   'PYMODULE'),
  ('setuptools._distutils.config',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.compat.py38',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\py38.py',
   'PYMODULE'),
  ('setuptools._distutils.compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\compat\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.upload',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\upload.py',
   'PYMODULE'),
  ('urllib.request',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\urllib\\request.py',
   'PYMODULE'),
  ('getpass', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\nturl2path.py',
   'PYMODULE'),
  ('ftplib', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('mimetypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\mimetypes.py',
   'PYMODULE'),
  ('http.cookiejar',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('http',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\http\\__init__.py',
   'PYMODULE'),
  ('ssl', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('urllib.response',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\urllib\\response.py',
   'PYMODULE'),
  ('urllib.error',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\urllib\\error.py',
   'PYMODULE'),
  ('http.client',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('setuptools._distutils.command.sdist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\sdist.py',
   'PYMODULE'),
  ('glob', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\glob.py', 'PYMODULE'),
  ('setuptools._distutils.command.register',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\register.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_scripts',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_lib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_lib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_headers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_headers.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_egg_info',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_egg_info.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install_data',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install_data.py',
   'PYMODULE'),
  ('setuptools._distutils.command.install',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\install.py',
   'PYMODULE'),
  ('site', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site.py', 'PYMODULE'),
  ('rlcompleter',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\rlcompleter.py',
   'PYMODULE'),
  ('_sitebuiltins',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_sitebuiltins.py',
   'PYMODULE'),
  ('pydoc', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('webbrowser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\webbrowser.py',
   'PYMODULE'),
  ('http.server',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('html',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\html\\__init__.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tty.py', 'PYMODULE'),
  ('pkgutil', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\zipimport.py',
   'PYMODULE'),
  ('setuptools._distutils.command.config',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\config.py',
   'PYMODULE'),
  ('setuptools._distutils.command.clean',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\clean.py',
   'PYMODULE'),
  ('setuptools._distutils.command.check',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\check.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_scripts',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_scripts.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_py',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_py.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_ext',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('queue', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\queue.py', 'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('multiprocessing.popen_forkserver',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('hmac', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('xmlrpc.client',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('xmlrpc.server',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xmlrpc\\server.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('multiprocessing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build_clib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build_clib.py',
   'PYMODULE'),
  ('setuptools._distutils.command.build',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\build.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_rpm',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_rpm.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist_dumb',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist_dumb.py',
   'PYMODULE'),
  ('setuptools._distutils.command.bdist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._distutils.command._framework_compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\_framework_compat.py',
   'PYMODULE'),
  ('setuptools._distutils.command',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\command\\__init__.py',
   'PYMODULE'),
  ('setuptools._distutils.cmd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\cmd.py',
   'PYMODULE'),
  ('setuptools._distutils.ccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\ccompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.bcppcompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\bcppcompiler.py',
   'PYMODULE'),
  ('setuptools._distutils.archive_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\archive_util.py',
   'PYMODULE'),
  ('setuptools._distutils._msvccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('unittest.mock',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\mock.py',
   'PYMODULE'),
  ('unittest',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('difflib', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('unittest.result',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('setuptools._distutils._modified',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_modified.py',
   'PYMODULE'),
  ('setuptools._distutils._macos_compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_macos_compat.py',
   'PYMODULE'),
  ('setuptools._distutils._log',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_log.py',
   'PYMODULE'),
  ('setuptools._distutils._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_itertools.py',
   'PYMODULE'),
  ('setuptools._distutils._functools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_functools.py',
   'PYMODULE'),
  ('setuptools._distutils._collections',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\_collections.py',
   'PYMODULE'),
  ('setuptools._distutils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_distutils\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.zipp',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\zipp.py',
   'PYMODULE'),
  ('setuptools._vendor.typing_extensions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._types',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_types.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._re',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_re.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli._parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.tomli',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\tomli\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.tags',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.specifiers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.requirements',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('typing_extensions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging.markers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._tokenizer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._structures',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._musllinux',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._manylinux',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging._elffile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('setuptools._vendor.packaging',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.ordered_set',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\ordered_set.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.recipes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools.more',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('setuptools._vendor.more_itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('setuptools.extern',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\extern\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.functools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco.context',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('setuptools._vendor.jaraco',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.simple',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.readers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources.abc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._legacy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._common',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources._adapters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_resources',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._py39compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_py39compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._meta',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._functools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._collections',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata._adapters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('setuptools._vendor.importlib_metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor.backports.tarfile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\tarfile.py',
   'PYMODULE'),
  ('setuptools._vendor.backports',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('setuptools._vendor',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_vendor\\__init__.py',
   'PYMODULE'),
  ('distutils.command.build_ext',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\command\\build_ext.py',
   'PYMODULE'),
  ('distutils._msvccompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\_msvccompiler.py',
   'PYMODULE'),
  ('distutils.extension',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\extension.py',
   'PYMODULE'),
  ('setuptools.warnings',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\warnings.py',
   'PYMODULE'),
  ('setuptools.extension',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\extension.py',
   'PYMODULE'),
  ('setuptools.dist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\dist.py',
   'PYMODULE'),
  ('setuptools.installer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\installer.py',
   'PYMODULE'),
  ('pkg_resources',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\__init__.py',
   'PYMODULE'),
  ('packaging.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\version.py',
   'PYMODULE'),
  ('packaging.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\utils.py',
   'PYMODULE'),
  ('packaging.tags',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\tags.py',
   'PYMODULE'),
  ('packaging.specifiers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\specifiers.py',
   'PYMODULE'),
  ('packaging.requirements',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\requirements.py',
   'PYMODULE'),
  ('packaging.metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\metadata.py',
   'PYMODULE'),
  ('packaging.markers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\markers.py',
   'PYMODULE'),
  ('packaging._tokenizer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('packaging._structures',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\_structures.py',
   'PYMODULE'),
  ('packaging._parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\_parser.py',
   'PYMODULE'),
  ('packaging._musllinux',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('packaging._manylinux',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('packaging._elffile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\_elffile.py',
   'PYMODULE'),
  ('packaging',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.zipp',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\zipp.py',
   'PYMODULE'),
  ('pkg_resources._vendor.typing_extensions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\typing_extensions.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.windows',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\windows.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.unix',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\unix.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.macos',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\macos.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.api',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.android',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\android.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs.__main__',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__main__.py',
   'PYMODULE'),
  ('platformdirs',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('platformdirs.android',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\android.py',
   'PYMODULE'),
  ('platformdirs.unix',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\unix.py',
   'PYMODULE'),
  ('platformdirs.macos',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\macos.py',
   'PYMODULE'),
  ('platformdirs.windows',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\windows.py',
   'PYMODULE'),
  ('platformdirs.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\version.py',
   'PYMODULE'),
  ('platformdirs.api',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\platformdirs\\api.py',
   'PYMODULE'),
  ('pkg_resources._vendor.platformdirs',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\platformdirs\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\version.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\utils.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.tags',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\tags.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.specifiers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\specifiers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.requirements',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\requirements.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\metadata.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging.markers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\markers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._tokenizer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_tokenizer.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._structures',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_structures.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_parser.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._musllinux',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_musllinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._manylinux',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_manylinux.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging._elffile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\_elffile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.packaging',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\packaging\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.recipes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\recipes.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools.more',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\more.py',
   'PYMODULE'),
  ('pkg_resources._vendor.more_itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\more_itertools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\text\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.functools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\functools\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco.context',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\context.py',
   'PYMODULE'),
  ('pkg_resources._vendor.jaraco',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\jaraco\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.simple',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\simple.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.readers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\readers.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources.abc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\abc.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._legacy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_legacy.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_itertools.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_compat.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._common',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_common.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources._adapters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\_adapters.py',
   'PYMODULE'),
  ('pkg_resources._vendor.importlib_resources',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\importlib_resources\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor.backports.tarfile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\backports\\tarfile.py',
   'PYMODULE'),
  ('pkg_resources._vendor.backports',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\backports\\__init__.py',
   'PYMODULE'),
  ('pkg_resources._vendor',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\_vendor\\__init__.py',
   'PYMODULE'),
  ('pkg_resources.extern',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pkg_resources\\extern\\__init__.py',
   'PYMODULE'),
  ('plistlib', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\plistlib.py', 'PYMODULE'),
  ('setuptools.wheel',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\wheel.py',
   'PYMODULE'),
  ('setuptools.archive_util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\archive_util.py',
   'PYMODULE'),
  ('setuptools._path',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_path.py',
   'PYMODULE'),
  ('setuptools.command.egg_info',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\egg_info.py',
   'PYMODULE'),
  ('setuptools.glob',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\glob.py',
   'PYMODULE'),
  ('setuptools.unicode_utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\unicode_utils.py',
   'PYMODULE'),
  ('setuptools.command.bdist_egg',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\bdist_egg.py',
   'PYMODULE'),
  ('setuptools.command.setopt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\setopt.py',
   'PYMODULE'),
  ('setuptools.command.sdist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command.build',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.build',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\command\\build.py',
   'PYMODULE'),
  ('distutils.command.sdist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\command\\sdist.py',
   'PYMODULE'),
  ('setuptools.command._requirestxt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\_requirestxt.py',
   'PYMODULE'),
  ('distutils.filelist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\filelist.py',
   'PYMODULE'),
  ('setuptools.config.pyprojecttoml',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\__init__.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_validations',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.fastjsonschema_exceptions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\fastjsonschema_exceptions.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.extra_validations',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\extra_validations.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.error_reporting',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\error_reporting.py',
   'PYMODULE'),
  ('json',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\json\\__init__.py',
   'PYMODULE'),
  ('json.encoder',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('setuptools.config._validate_pyproject.formats',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_validate_pyproject\\formats.py',
   'PYMODULE'),
  ('setuptools.compat.py310',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\compat\\py310.py',
   'PYMODULE'),
  ('setuptools.compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\compat\\__init__.py',
   'PYMODULE'),
  ('tomllib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tomllib\\__init__.py',
   'PYMODULE'),
  ('tomllib._parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tomllib\\_parser.py',
   'PYMODULE'),
  ('tomllib._types',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tomllib\\_types.py',
   'PYMODULE'),
  ('tomllib._re',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tomllib\\_re.py',
   'PYMODULE'),
  ('setuptools.config._apply_pyprojecttoml',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\_apply_pyprojecttoml.py',
   'PYMODULE'),
  ('setuptools.config.expand',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\expand.py',
   'PYMODULE'),
  ('setuptools.errors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\errors.py',
   'PYMODULE'),
  ('setuptools.config.setupcfg',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\setupcfg.py',
   'PYMODULE'),
  ('setuptools.config',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\config\\__init__.py',
   'PYMODULE'),
  ('setuptools._importlib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_importlib.py',
   'PYMODULE'),
  ('importlib_metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib_metadata._functools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_functools.py',
   'PYMODULE'),
  ('importlib_metadata._compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_compat.py',
   'PYMODULE'),
  ('importlib_metadata._collections',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_collections.py',
   'PYMODULE'),
  ('importlib_metadata.compat.py39',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\compat\\py39.py',
   'PYMODULE'),
  ('importlib_metadata.compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\compat\\__init__.py',
   'PYMODULE'),
  ('importlib_metadata._meta',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_meta.py',
   'PYMODULE'),
  ('importlib_metadata._adapters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib_metadata._text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata\\_text.py',
   'PYMODULE'),
  ('zipp',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\zipp\\__init__.py',
   'PYMODULE'),
  ('zipp.glob',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\zipp\\glob.py',
   'PYMODULE'),
  ('zipp.py310compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\zipp\\py310compat.py',
   'PYMODULE'),
  ('setuptools.windows_support',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\windows_support.py',
   'PYMODULE'),
  ('setuptools.command',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\command\\__init__.py',
   'PYMODULE'),
  ('distutils.command.bdist',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\command\\bdist.py',
   'PYMODULE'),
  ('setuptools._entry_points',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_entry_points.py',
   'PYMODULE'),
  ('setuptools._itertools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_itertools.py',
   'PYMODULE'),
  ('setuptools.discovery',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\discovery.py',
   'PYMODULE'),
  ('setuptools.depends',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\depends.py',
   'PYMODULE'),
  ('setuptools._imp',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_imp.py',
   'PYMODULE'),
  ('setuptools.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\version.py',
   'PYMODULE'),
  ('setuptools.logging',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\logging.py',
   'PYMODULE'),
  ('setuptools.monkey',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\monkey.py',
   'PYMODULE'),
  ('setuptools.msvc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\msvc.py',
   'PYMODULE'),
  ('setuptools._core_metadata',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_core_metadata.py',
   'PYMODULE'),
  ('setuptools._reqs',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_reqs.py',
   'PYMODULE'),
  ('setuptools._normalization',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\setuptools\\_normalization.py',
   'PYMODULE'),
  ('distutils.util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\util.py',
   'PYMODULE'),
  ('distutils.errors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\errors.py',
   'PYMODULE'),
  ('distutils.core',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\core.py',
   'PYMODULE'),
  ('distutils.config',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\distutils\\config.py',
   'PYMODULE'),
  ('cgi', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\cgi.py', 'PYMODULE'),
  ('_distutils_hack.override',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\_distutils_hack\\override.py',
   'PYMODULE'),
  ('_pyi_rth_utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PyInstaller\\fake-modules\\_pyi_rth_utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell._writer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\cell\\_writer.py',
   'PYMODULE'),
  ('openpyxl.cell',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\cell\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.read_only',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\cell\\read_only.py',
   'PYMODULE'),
  ('openpyxl.styles.numbers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\numbers.py',
   'PYMODULE'),
  ('openpyxl.descriptors.serialisable',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\serialisable.py',
   'PYMODULE'),
  ('openpyxl.descriptors.namespace',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\namespace.py',
   'PYMODULE'),
  ('openpyxl.descriptors.sequence',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\sequence.py',
   'PYMODULE'),
  ('openpyxl.descriptors.base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\base.py',
   'PYMODULE'),
  ('openpyxl.utils.indexed_list',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\indexed_list.py',
   'PYMODULE'),
  ('openpyxl.descriptors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\__init__.py',
   'PYMODULE'),
  ('openpyxl.cell.cell',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\cell\\cell.py',
   'PYMODULE'),
  ('openpyxl.worksheet.hyperlink',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\hyperlink.py',
   'PYMODULE'),
  ('openpyxl.worksheet',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.descriptors.excel',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\excel.py',
   'PYMODULE'),
  ('openpyxl.xml.constants',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\xml\\constants.py',
   'PYMODULE'),
  ('openpyxl.xml',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\xml\\__init__.py',
   'PYMODULE'),
  ('defusedxml',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\__init__.py',
   'PYMODULE'),
  ('defusedxml.xmlrpc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\xmlrpc.py',
   'PYMODULE'),
  ('defusedxml.sax',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\sax.py',
   'PYMODULE'),
  ('defusedxml.minidom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\minidom.py',
   'PYMODULE'),
  ('xml.dom.minidom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\minidom.py',
   'PYMODULE'),
  ('xml.dom.pulldom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\pulldom.py',
   'PYMODULE'),
  ('xml.dom.expatbuilder',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\expatbuilder.py',
   'PYMODULE'),
  ('xml.dom.NodeFilter',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\NodeFilter.py',
   'PYMODULE'),
  ('xml.dom.xmlbuilder',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\xmlbuilder.py',
   'PYMODULE'),
  ('xml.dom.minicompat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\minicompat.py',
   'PYMODULE'),
  ('xml.dom.domreg',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\domreg.py',
   'PYMODULE'),
  ('xml.dom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\dom\\__init__.py',
   'PYMODULE'),
  ('defusedxml.pulldom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\pulldom.py',
   'PYMODULE'),
  ('defusedxml.expatreader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\expatreader.py',
   'PYMODULE'),
  ('defusedxml.expatbuilder',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\expatbuilder.py',
   'PYMODULE'),
  ('defusedxml.ElementTree',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementTree',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\etree\\ElementTree.py',
   'PYMODULE'),
  ('xml.etree.cElementTree',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\etree\\cElementTree.py',
   'PYMODULE'),
  ('xml.etree.ElementInclude',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\etree\\ElementInclude.py',
   'PYMODULE'),
  ('xml.etree.ElementPath',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\etree\\ElementPath.py',
   'PYMODULE'),
  ('xml.etree',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\xml\\etree\\__init__.py',
   'PYMODULE'),
  ('defusedxml.cElementTree',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\cElementTree.py',
   'PYMODULE'),
  ('defusedxml.common',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\defusedxml\\common.py',
   'PYMODULE'),
  ('openpyxl.styles.styleable',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\styleable.py',
   'PYMODULE'),
  ('openpyxl.styles.builtins',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\builtins.py',
   'PYMODULE'),
  ('openpyxl.styles.named_styles',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\named_styles.py',
   'PYMODULE'),
  ('openpyxl.styles.protection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\protection.py',
   'PYMODULE'),
  ('openpyxl.styles.alignment',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\alignment.py',
   'PYMODULE'),
  ('openpyxl.styles.borders',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\borders.py',
   'PYMODULE'),
  ('openpyxl.styles.colors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\colors.py',
   'PYMODULE'),
  ('openpyxl.styles.fonts',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\fonts.py',
   'PYMODULE'),
  ('openpyxl.descriptors.nested',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\descriptors\\nested.py',
   'PYMODULE'),
  ('openpyxl.styles.fills',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\fills.py',
   'PYMODULE'),
  ('openpyxl.styles.cell_style',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\cell_style.py',
   'PYMODULE'),
  ('openpyxl.styles.proxy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\proxy.py',
   'PYMODULE'),
  ('openpyxl.utils.exceptions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\exceptions.py',
   'PYMODULE'),
  ('openpyxl.cell.rich_text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\cell\\rich_text.py',
   'PYMODULE'),
  ('openpyxl.cell.text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\cell\\text.py',
   'PYMODULE'),
  ('openpyxl.worksheet.formula',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\formula.py',
   'PYMODULE'),
  ('openpyxl.utils.datetime',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\datetime.py',
   'PYMODULE'),
  ('openpyxl.xml.functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\xml\\functions.py',
   'PYMODULE'),
  ('et_xmlfile.xmlfile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\et_xmlfile\\xmlfile.py',
   'PYMODULE'),
  ('et_xmlfile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\et_xmlfile\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\compat\\__init__.py',
   'PYMODULE'),
  ('openpyxl.compat.strings',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\compat\\strings.py',
   'PYMODULE'),
  ('openpyxl.compat.numbers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\compat\\numbers.py',
   'PYMODULE'),
  ('numpy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\__init__.py',
   'PYMODULE'),
  ('six',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\six.py',
   'PYMODULE'),
  ('numpy.core._dtype_ctypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_dtype_ctypes.py',
   'PYMODULE'),
  ('numpy.version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\version.py',
   'PYMODULE'),
  ('numpy._version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_version.py',
   'PYMODULE'),
  ('numpy._pytesttester',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_pytesttester.py',
   'PYMODULE'),
  ('numpy.testing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\__init__.py',
   'PYMODULE'),
  ('numpy.testing._private.nosetester',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\nosetester.py',
   'PYMODULE'),
  ('numpy.distutils.cpuinfo',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\distutils\\cpuinfo.py',
   'PYMODULE'),
  ('numpy.distutils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\distutils\\__init__.py',
   'PYMODULE'),
  ('doctest', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\doctest.py', 'PYMODULE'),
  ('pdb', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\pdb.py', 'PYMODULE'),
  ('code', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\cmd.py', 'PYMODULE'),
  ('numpy.testing._private.noseclasses',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\noseclasses.py',
   'PYMODULE'),
  ('numpy.testing._private.decorators',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\decorators.py',
   'PYMODULE'),
  ('numpy.testing._private.parameterized',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\parameterized.py',
   'PYMODULE'),
  ('numpy.testing._private.extbuild',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\extbuild.py',
   'PYMODULE'),
  ('numpy.testing._private.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\utils.py',
   'PYMODULE'),
  ('psutil',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\psutil\\__init__.py',
   'PYMODULE'),
  ('psutil._pswindows',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\psutil\\_pswindows.py',
   'PYMODULE'),
  ('psutil._compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\psutil\\_compat.py',
   'PYMODULE'),
  ('psutil._common',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\psutil\\_common.py',
   'PYMODULE'),
  ('numpy.core.fromnumeric',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\fromnumeric.py',
   'PYMODULE'),
  ('numpy.core._methods',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_methods.py',
   'PYMODULE'),
  ('numpy.lib.stride_tricks',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\stride_tricks.py',
   'PYMODULE'),
  ('numpy.core.numeric',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\numeric.py',
   'PYMODULE'),
  ('numpy.core._asarray',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_asarray.py',
   'PYMODULE'),
  ('numpy.core.arrayprint',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\arrayprint.py',
   'PYMODULE'),
  ('numpy.core.shape_base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\shape_base.py',
   'PYMODULE'),
  ('numpy.core._ufunc_config',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_ufunc_config.py',
   'PYMODULE'),
  ('numpy.core._exceptions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_exceptions.py',
   'PYMODULE'),
  ('numpy.core.umath',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\umath.py',
   'PYMODULE'),
  ('numpy.core.overrides',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\overrides.py',
   'PYMODULE'),
  ('numpy.compat._inspect',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\compat\\_inspect.py',
   'PYMODULE'),
  ('numpy.core.multiarray',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\multiarray.py',
   'PYMODULE'),
  ('numpy.core.numerictypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\numerictypes.py',
   'PYMODULE'),
  ('numpy.core._dtype',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_dtype.py',
   'PYMODULE'),
  ('numpy.core._type_aliases',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_type_aliases.py',
   'PYMODULE'),
  ('numpy.core._string_helpers',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_string_helpers.py',
   'PYMODULE'),
  ('numpy.testing._private',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\testing\\_private\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\matrixlib\\__init__.py',
   'PYMODULE'),
  ('numpy.matrixlib.defmatrix',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\matrixlib\\defmatrix.py',
   'PYMODULE'),
  ('numpy.ma',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\ma\\__init__.py',
   'PYMODULE'),
  ('numpy.ma.extras',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\ma\\extras.py',
   'PYMODULE'),
  ('numpy.lib.index_tricks',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\index_tricks.py',
   'PYMODULE'),
  ('numpy.lib.function_base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\function_base.py',
   'PYMODULE'),
  ('numpy.lib.histograms',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\histograms.py',
   'PYMODULE'),
  ('numpy.lib.twodim_base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\twodim_base.py',
   'PYMODULE'),
  ('numpy.core.function_base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\function_base.py',
   'PYMODULE'),
  ('numpy.ma.core',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\ma\\core.py',
   'PYMODULE'),
  ('numpy.ctypeslib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\ctypeslib.py',
   'PYMODULE'),
  ('numpy.core._internal',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_internal.py',
   'PYMODULE'),
  ('numpy.random',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\__init__.py',
   'PYMODULE'),
  ('numpy.random._pickle',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_pickle.py',
   'PYMODULE'),
  ('numpy.polynomial',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\__init__.py',
   'PYMODULE'),
  ('numpy.polynomial._polybase',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\_polybase.py',
   'PYMODULE'),
  ('numpy.polynomial.laguerre',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\laguerre.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite_e',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite_e.py',
   'PYMODULE'),
  ('numpy.polynomial.hermite',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\hermite.py',
   'PYMODULE'),
  ('numpy.polynomial.legendre',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\legendre.py',
   'PYMODULE'),
  ('numpy.polynomial.chebyshev',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\chebyshev.py',
   'PYMODULE'),
  ('numpy.polynomial.polynomial',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\polynomial.py',
   'PYMODULE'),
  ('numpy.polynomial.polyutils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\polynomial\\polyutils.py',
   'PYMODULE'),
  ('numpy.fft',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\fft\\__init__.py',
   'PYMODULE'),
  ('numpy.fft.helper',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\fft\\helper.py',
   'PYMODULE'),
  ('numpy.fft._pocketfft',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft.py',
   'PYMODULE'),
  ('numpy.linalg',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\__init__.py',
   'PYMODULE'),
  ('numpy.linalg.linalg',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\linalg.py',
   'PYMODULE'),
  ('numpy.lib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\__init__.py',
   'PYMODULE'),
  ('numpy.lib._version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_version.py',
   'PYMODULE'),
  ('numpy.lib.arraypad',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\arraypad.py',
   'PYMODULE'),
  ('numpy.lib.arrayterator',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\arrayterator.py',
   'PYMODULE'),
  ('numpy.lib.npyio',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\npyio.py',
   'PYMODULE'),
  ('numpy.ma.mrecords',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\ma\\mrecords.py',
   'PYMODULE'),
  ('numpy.core.records',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\records.py',
   'PYMODULE'),
  ('numpy.lib._iotools',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_iotools.py',
   'PYMODULE'),
  ('numpy.lib._datasource',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\_datasource.py',
   'PYMODULE'),
  ('numpy.lib.format',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\format.py',
   'PYMODULE'),
  ('numpy.lib.arraysetops',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\arraysetops.py',
   'PYMODULE'),
  ('numpy.lib.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\utils.py',
   'PYMODULE'),
  ('numpy.lib.polynomial',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\polynomial.py',
   'PYMODULE'),
  ('numpy.lib.ufunclike',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\ufunclike.py',
   'PYMODULE'),
  ('numpy.lib.shape_base',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\shape_base.py',
   'PYMODULE'),
  ('numpy.lib.nanfunctions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\nanfunctions.py',
   'PYMODULE'),
  ('numpy.lib.type_check',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\type_check.py',
   'PYMODULE'),
  ('numpy.core.getlimits',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\getlimits.py',
   'PYMODULE'),
  ('numpy.core._machar',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_machar.py',
   'PYMODULE'),
  ('numpy.lib.scimath',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\scimath.py',
   'PYMODULE'),
  ('numpy.lib.mixins',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\lib\\mixins.py',
   'PYMODULE'),
  ('numpy.compat',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\compat\\__init__.py',
   'PYMODULE'),
  ('numpy.compat.py3k',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\compat\\py3k.py',
   'PYMODULE'),
  ('numpy.core',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\__init__.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs_scalars',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs_scalars.py',
   'PYMODULE'),
  ('numpy.core._add_newdocs',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_add_newdocs.py',
   'PYMODULE'),
  ('numpy.core.einsumfunc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\einsumfunc.py',
   'PYMODULE'),
  ('numpy.core.memmap',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\memmap.py',
   'PYMODULE'),
  ('numpy.core.defchararray',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\defchararray.py',
   'PYMODULE'),
  ('numpy._distributor_init',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_distributor_init.py',
   'PYMODULE'),
  ('numpy.__config__',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\__config__.py',
   'PYMODULE'),
  ('numpy.array_api',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\__init__.py',
   'PYMODULE'),
  ('numpy.array_api._utility_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_utility_functions.py',
   'PYMODULE'),
  ('numpy.array_api._array_object',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_array_object.py',
   'PYMODULE'),
  ('numpy.typing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._add_docstring',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_add_docstring.py',
   'PYMODULE'),
  ('numpy._typing._generic_alias',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_generic_alias.py',
   'PYMODULE'),
  ('numpy._typing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\__init__.py',
   'PYMODULE'),
  ('numpy._typing._array_like',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_array_like.py',
   'PYMODULE'),
  ('numpy._typing._dtype_like',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_dtype_like.py',
   'PYMODULE'),
  ('numpy._typing._shape',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_shape.py',
   'PYMODULE'),
  ('numpy._typing._scalars',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_scalars.py',
   'PYMODULE'),
  ('numpy._typing._char_codes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_char_codes.py',
   'PYMODULE'),
  ('numpy._typing._nbit',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_nbit.py',
   'PYMODULE'),
  ('numpy._typing._nested_sequence',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_typing\\_nested_sequence.py',
   'PYMODULE'),
  ('numpy.array_api._typing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_typing.py',
   'PYMODULE'),
  ('numpy.array_api._statistical_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_statistical_functions.py',
   'PYMODULE'),
  ('numpy.array_api._sorting_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_sorting_functions.py',
   'PYMODULE'),
  ('numpy.array_api._set_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_set_functions.py',
   'PYMODULE'),
  ('numpy.array_api._searching_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_searching_functions.py',
   'PYMODULE'),
  ('numpy.array_api._manipulation_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_manipulation_functions.py',
   'PYMODULE'),
  ('numpy.array_api.linalg',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\linalg.py',
   'PYMODULE'),
  ('numpy.array_api._elementwise_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_elementwise_functions.py',
   'PYMODULE'),
  ('numpy.array_api._dtypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_dtypes.py',
   'PYMODULE'),
  ('numpy.array_api._data_type_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_data_type_functions.py',
   'PYMODULE'),
  ('numpy.array_api._creation_functions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_creation_functions.py',
   'PYMODULE'),
  ('numpy.array_api._constants',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\array_api\\_constants.py',
   'PYMODULE'),
  ('numpy._globals',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\_globals.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('stringprep',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\stringprep.py',
   'PYMODULE'),
  ('tracemalloc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_strptime',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\_strptime.py',
   'PYMODULE'),
  ('tempfile', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('shutil', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('datetime', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('copy', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\copy.py', 'PYMODULE'),
  ('openpyxl.utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\__init__.py',
   'PYMODULE'),
  ('openpyxl.utils.formulas',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\formulas.py',
   'PYMODULE'),
  ('openpyxl.formula',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\formula\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formula.tokenizer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\formula\\tokenizer.py',
   'PYMODULE'),
  ('openpyxl.utils.cell',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\cell.py',
   'PYMODULE'),
  ('openpyxl.styles',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\__init__.py',
   'PYMODULE'),
  ('openpyxl',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\__init__.py',
   'PYMODULE'),
  ('openpyxl._constants',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\_constants.py',
   'PYMODULE'),
  ('openpyxl.reader.excel',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\reader\\excel.py',
   'PYMODULE'),
  ('openpyxl.reader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\reader\\__init__.py',
   'PYMODULE'),
  ('openpyxl.reader.drawings',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\reader\\drawings.py',
   'PYMODULE'),
  ('openpyxl.chart.reader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\reader.py',
   'PYMODULE'),
  ('openpyxl.chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chart.reference',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\reference.py',
   'PYMODULE'),
  ('openpyxl.worksheet.worksheet',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\worksheet.py',
   'PYMODULE'),
  ('openpyxl.worksheet.print_settings',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.worksheet.scenario',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\scenario.py',
   'PYMODULE'),
  ('openpyxl.worksheet.pagebreak',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\pagebreak.py',
   'PYMODULE'),
  ('openpyxl.worksheet.properties',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.worksheet.merge',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\merge.py',
   'PYMODULE'),
  ('openpyxl.worksheet.cell_range',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\cell_range.py',
   'PYMODULE'),
  ('openpyxl.worksheet.views',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\views.py',
   'PYMODULE'),
  ('openpyxl.worksheet.filters',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\filters.py',
   'PYMODULE'),
  ('openpyxl.worksheet.protection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.utils.protection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\protection.py',
   'PYMODULE'),
  ('openpyxl.worksheet.dimensions',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\dimensions.py',
   'PYMODULE'),
  ('openpyxl.utils.bound_dictionary',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\bound_dictionary.py',
   'PYMODULE'),
  ('openpyxl.utils.units',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\units.py',
   'PYMODULE'),
  ('openpyxl.worksheet.page',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\page.py',
   'PYMODULE'),
  ('openpyxl.worksheet.datavalidation',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\datavalidation.py',
   'PYMODULE'),
  ('openpyxl.formula.translate',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\formula\\translate.py',
   'PYMODULE'),
  ('openpyxl.workbook.defined_name',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\defined_name.py',
   'PYMODULE'),
  ('openpyxl.workbook.child',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\child.py',
   'PYMODULE'),
  ('openpyxl.worksheet.header_footer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\header_footer.py',
   'PYMODULE'),
  ('openpyxl.utils.escape',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\utils\\escape.py',
   'PYMODULE'),
  ('openpyxl.formatting.formatting',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\formatting\\formatting.py',
   'PYMODULE'),
  ('openpyxl.formatting',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\formatting\\__init__.py',
   'PYMODULE'),
  ('openpyxl.formatting.rule',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\formatting\\rule.py',
   'PYMODULE'),
  ('openpyxl.styles.differential',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\differential.py',
   'PYMODULE'),
  ('openpyxl.chart.series_factory',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\series_factory.py',
   'PYMODULE'),
  ('openpyxl.chart.series',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\series.py',
   'PYMODULE'),
  ('openpyxl.chart.trendline',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\trendline.py',
   'PYMODULE'),
  ('openpyxl.chart.layout',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\layout.py',
   'PYMODULE'),
  ('openpyxl.chart.text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing.text',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\text.py',
   'PYMODULE'),
  ('openpyxl.drawing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\__init__.py',
   'PYMODULE'),
  ('openpyxl.drawing.drawing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.geometry',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\geometry.py',
   'PYMODULE'),
  ('openpyxl.drawing.line',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\line.py',
   'PYMODULE'),
  ('openpyxl.drawing.fill',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\fill.py',
   'PYMODULE'),
  ('openpyxl.drawing.effect',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\effect.py',
   'PYMODULE'),
  ('openpyxl.drawing.colors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\colors.py',
   'PYMODULE'),
  ('openpyxl.chart.marker',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\marker.py',
   'PYMODULE'),
  ('openpyxl.chart.picture',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\picture.py',
   'PYMODULE'),
  ('openpyxl.chart.label',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\label.py',
   'PYMODULE'),
  ('openpyxl.chart.error_bar',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\error_bar.py',
   'PYMODULE'),
  ('openpyxl.chart.shapes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\shapes.py',
   'PYMODULE'),
  ('openpyxl.chart.data_source',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\data_source.py',
   'PYMODULE'),
  ('openpyxl.chart.surface_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\surface_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.axis',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\axis.py',
   'PYMODULE'),
  ('openpyxl.chart.title',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\title.py',
   'PYMODULE'),
  ('openpyxl.chart.descriptors',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\descriptors.py',
   'PYMODULE'),
  ('openpyxl.chart._3d',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\_3d.py',
   'PYMODULE'),
  ('openpyxl.chart._chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.legend',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\legend.py',
   'PYMODULE'),
  ('openpyxl.chart.stock_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\stock_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.updown_bars',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\updown_bars.py',
   'PYMODULE'),
  ('openpyxl.chart.scatter_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\scatter_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.radar_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\radar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.pie_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\pie_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.line_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\line_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bubble_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\bubble_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.bar_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\bar_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.area_chart',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\area_chart.py',
   'PYMODULE'),
  ('openpyxl.chart.chartspace',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\chartspace.py',
   'PYMODULE'),
  ('openpyxl.chart.print_settings',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\print_settings.py',
   'PYMODULE'),
  ('openpyxl.chart.pivot',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\pivot.py',
   'PYMODULE'),
  ('openpyxl.chart.plotarea',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chart\\plotarea.py',
   'PYMODULE'),
  ('openpyxl.drawing.image',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\image.py',
   'PYMODULE'),
  ('PIL.Image',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\Image.py',
   'PYMODULE'),
  ('PIL.XpmImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\XpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XbmImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\XbmImagePlugin.py',
   'PYMODULE'),
  ('PIL.XVThumbImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\XVThumbImagePlugin.py',
   'PYMODULE'),
  ('PIL.WmfImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\WmfImagePlugin.py',
   'PYMODULE'),
  ('PIL.WebPImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\WebPImagePlugin.py',
   'PYMODULE'),
  ('PIL.TgaImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\TgaImagePlugin.py',
   'PYMODULE'),
  ('PIL.SunImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\SunImagePlugin.py',
   'PYMODULE'),
  ('PIL.SpiderImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\SpiderImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageTk',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageTk.py',
   'PYMODULE'),
  ('PIL.SgiImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\SgiImagePlugin.py',
   'PYMODULE'),
  ('PIL.QoiImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\QoiImagePlugin.py',
   'PYMODULE'),
  ('PIL.PsdImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PsdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PixarImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PixarImagePlugin.py',
   'PYMODULE'),
  ('PIL.PdfImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PdfImagePlugin.py',
   'PYMODULE'),
  ('PIL.features',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\features.py',
   'PYMODULE'),
  ('PIL.PdfParser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PdfParser.py',
   'PYMODULE'),
  ('PIL.ImageSequence',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageSequence.py',
   'PYMODULE'),
  ('PIL.PcxImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.PcdImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PcdImagePlugin.py',
   'PYMODULE'),
  ('PIL.PalmImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PalmImagePlugin.py',
   'PYMODULE'),
  ('PIL.MspImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\MspImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpoImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\MpoImagePlugin.py',
   'PYMODULE'),
  ('PIL.MpegImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\MpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.MicImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\MicImagePlugin.py',
   'PYMODULE'),
  ('PIL.McIdasImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\McIdasImagePlugin.py',
   'PYMODULE'),
  ('PIL.Jpeg2KImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\Jpeg2KImagePlugin.py',
   'PYMODULE'),
  ('PIL.IptcImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\IptcImagePlugin.py',
   'PYMODULE'),
  ('PIL._deprecate',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_deprecate.py',
   'PYMODULE'),
  ('PIL.ImtImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImtImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcoImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\IcoImagePlugin.py',
   'PYMODULE'),
  ('PIL.IcnsImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\IcnsImagePlugin.py',
   'PYMODULE'),
  ('PIL.Hdf5StubImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\Hdf5StubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GribStubImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\GribStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.GbrImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\GbrImagePlugin.py',
   'PYMODULE'),
  ('PIL.FtexImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\FtexImagePlugin.py',
   'PYMODULE'),
  ('PIL.FpxImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\FpxImagePlugin.py',
   'PYMODULE'),
  ('PIL.FliImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\FliImagePlugin.py',
   'PYMODULE'),
  ('PIL.FitsImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\FitsImagePlugin.py',
   'PYMODULE'),
  ('PIL.EpsImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\EpsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DdsImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\DdsImagePlugin.py',
   'PYMODULE'),
  ('PIL.DcxImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\DcxImagePlugin.py',
   'PYMODULE'),
  ('PIL.CurImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\CurImagePlugin.py',
   'PYMODULE'),
  ('PIL.BufrStubImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\BufrStubImagePlugin.py',
   'PYMODULE'),
  ('PIL.BlpImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\BlpImagePlugin.py',
   'PYMODULE'),
  ('PIL.TiffImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\TiffImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageOps',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageOps.py',
   'PYMODULE'),
  ('PIL.ImageShow',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageShow.py',
   'PYMODULE'),
  ('PIL.ImageQt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageQt.py',
   'PYMODULE'),
  ('PIL.ImageColor',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageColor.py',
   'PYMODULE'),
  ('colorsys', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\colorsys.py', 'PYMODULE'),
  ('PIL.ImageFilter',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageFilter.py',
   'PYMODULE'),
  ('PIL.ImageCms',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageCms.py',
   'PYMODULE'),
  ('PIL.ImageWin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageWin.py',
   'PYMODULE'),
  ('PIL.PyAccess',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PyAccess.py',
   'PYMODULE'),
  ('PIL.ImagePalette',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImagePalette.py',
   'PYMODULE'),
  ('PIL.PaletteFile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpPaletteFile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\GimpPaletteFile.py',
   'PYMODULE'),
  ('PIL.GimpGradientFile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\GimpGradientFile.py',
   'PYMODULE'),
  ('PIL.PngImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PngImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageChops',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageChops.py',
   'PYMODULE'),
  ('PIL.PpmImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\PpmImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\JpegImagePlugin.py',
   'PYMODULE'),
  ('PIL.JpegPresets',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\JpegPresets.py',
   'PYMODULE'),
  ('PIL.GifImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\GifImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageMath',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageMath.py',
   'PYMODULE'),
  ('PIL.BmpImagePlugin',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\BmpImagePlugin.py',
   'PYMODULE'),
  ('PIL.ImageFile',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageFile.py',
   'PYMODULE'),
  ('cffi',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\__init__.py',
   'PYMODULE'),
  ('cffi.error',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\error.py',
   'PYMODULE'),
  ('cffi.api',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\api.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('cffi.recompiler',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\recompiler.py',
   'PYMODULE'),
  ('cffi.cffi_opcode',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\cffi_opcode.py',
   'PYMODULE'),
  ('cffi._shimmed_dist_utils',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\_shimmed_dist_utils.py',
   'PYMODULE'),
  ('cffi.verifier',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\verifier.py',
   'PYMODULE'),
  ('imp', 'D:\\Users\\ZhangLan\\miniconda3\\Lib\\imp.py', 'PYMODULE'),
  ('cffi.lock',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\lock.py',
   'PYMODULE'),
  ('cffi.pkgconfig',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\pkgconfig.py',
   'PYMODULE'),
  ('cffi.vengine_cpy',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\vengine_cpy.py',
   'PYMODULE'),
  ('cffi._imp_emulation',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\_imp_emulation.py',
   'PYMODULE'),
  ('cffi.vengine_gen',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\vengine_gen.py',
   'PYMODULE'),
  ('cffi.ffiplatform',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\ffiplatform.py',
   'PYMODULE'),
  ('cffi.cparser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\cparser.py',
   'PYMODULE'),
  ('pycparser.lextab',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\lextab.py',
   'PYMODULE'),
  ('pycparser.yacctab',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\yacctab.py',
   'PYMODULE'),
  ('pycparser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_parser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\c_parser.py',
   'PYMODULE'),
  ('pycparser.ast_transforms',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\ast_transforms.py',
   'PYMODULE'),
  ('pycparser.plyparser',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\plyparser.py',
   'PYMODULE'),
  ('pycparser.c_lexer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\c_lexer.py',
   'PYMODULE'),
  ('pycparser.ply.lex',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\ply\\lex.py',
   'PYMODULE'),
  ('pycparser.ply.yacc',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\ply\\yacc.py',
   'PYMODULE'),
  ('pycparser.ply',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\ply\\__init__.py',
   'PYMODULE'),
  ('pycparser.c_ast',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\pycparser\\c_ast.py',
   'PYMODULE'),
  ('cffi.commontypes',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\commontypes.py',
   'PYMODULE'),
  ('cffi.model',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\cffi\\model.py',
   'PYMODULE'),
  ('PIL._util',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_util.py',
   'PYMODULE'),
  ('PIL._typing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_typing.py',
   'PYMODULE'),
  ('PIL._binary',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_binary.py',
   'PYMODULE'),
  ('PIL.TiffTags',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\TiffTags.py',
   'PYMODULE'),
  ('PIL.ImageMode',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ImageMode.py',
   'PYMODULE'),
  ('PIL.ExifTags',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\ExifTags.py',
   'PYMODULE'),
  ('PIL',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\__init__.py',
   'PYMODULE'),
  ('PIL._version',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_version.py',
   'PYMODULE'),
  ('openpyxl.drawing.spreadsheet_drawing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\spreadsheet_drawing.py',
   'PYMODULE'),
  ('openpyxl.drawing.relation',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\relation.py',
   'PYMODULE'),
  ('openpyxl.drawing.picture',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\picture.py',
   'PYMODULE'),
  ('openpyxl.drawing.properties',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\properties.py',
   'PYMODULE'),
  ('openpyxl.drawing.graphic',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\graphic.py',
   'PYMODULE'),
  ('openpyxl.drawing.connector',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\connector.py',
   'PYMODULE'),
  ('openpyxl.drawing.xdr',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\drawing\\xdr.py',
   'PYMODULE'),
  ('openpyxl.worksheet.table',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\table.py',
   'PYMODULE'),
  ('openpyxl.worksheet.related',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\related.py',
   'PYMODULE'),
  ('openpyxl.chartsheet',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\__init__.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.chartsheet',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\chartsheet.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.publish',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\publish.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.custom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\custom.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.views',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\views.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.protection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\protection.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.properties',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\properties.py',
   'PYMODULE'),
  ('openpyxl.chartsheet.relation',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\chartsheet\\relation.py',
   'PYMODULE'),
  ('openpyxl.worksheet.drawing',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\drawing.py',
   'PYMODULE'),
  ('openpyxl.worksheet._reader',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\_reader.py',
   'PYMODULE'),
  ('openpyxl.worksheet._read_only',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\_read_only.py',
   'PYMODULE'),
  ('openpyxl.packaging.relationship',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\relationship.py',
   'PYMODULE'),
  ('openpyxl.packaging',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.manifest',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\manifest.py',
   'PYMODULE'),
  ('openpyxl.packaging.custom',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\custom.py',
   'PYMODULE'),
  ('openpyxl.packaging.core',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\core.py',
   'PYMODULE'),
  ('openpyxl.styles.stylesheet',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\stylesheet.py',
   'PYMODULE'),
  ('openpyxl.styles.table',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\styles\\table.py',
   'PYMODULE'),
  ('openpyxl.reader.workbook',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\reader\\workbook.py',
   'PYMODULE'),
  ('openpyxl.pivot.record',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\pivot\\record.py',
   'PYMODULE'),
  ('openpyxl.pivot',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\pivot\\__init__.py',
   'PYMODULE'),
  ('openpyxl.pivot.fields',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\pivot\\fields.py',
   'PYMODULE'),
  ('openpyxl.pivot.cache',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\pivot\\cache.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link.external',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\external.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_link',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\external_link\\__init__.py',
   'PYMODULE'),
  ('openpyxl.packaging.workbook',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\workbook.py',
   'PYMODULE'),
  ('openpyxl.workbook.web',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\web.py',
   'PYMODULE'),
  ('openpyxl.workbook.views',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\views.py',
   'PYMODULE'),
  ('openpyxl.workbook.smart_tags',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\smart_tags.py',
   'PYMODULE'),
  ('openpyxl.workbook.protection',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\protection.py',
   'PYMODULE'),
  ('openpyxl.workbook.properties',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\properties.py',
   'PYMODULE'),
  ('openpyxl.workbook.function_group',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\function_group.py',
   'PYMODULE'),
  ('openpyxl.workbook.external_reference',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\external_reference.py',
   'PYMODULE'),
  ('openpyxl.reader.strings',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\reader\\strings.py',
   'PYMODULE'),
  ('openpyxl.comments.comment_sheet',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\comments\\comment_sheet.py',
   'PYMODULE'),
  ('openpyxl.comments',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\comments\\__init__.py',
   'PYMODULE'),
  ('openpyxl.comments.shape_writer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\comments\\shape_writer.py',
   'PYMODULE'),
  ('openpyxl.comments.comments',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\comments\\comments.py',
   'PYMODULE'),
  ('openpyxl.comments.author',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\comments\\author.py',
   'PYMODULE'),
  ('openpyxl.pivot.table',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\pivot\\table.py',
   'PYMODULE'),
  ('openpyxl.workbook',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\__init__.py',
   'PYMODULE'),
  ('openpyxl.workbook.workbook',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\workbook.py',
   'PYMODULE'),
  ('openpyxl.writer.excel',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\writer\\excel.py',
   'PYMODULE'),
  ('openpyxl.writer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\writer\\__init__.py',
   'PYMODULE'),
  ('openpyxl.writer.theme',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\writer\\theme.py',
   'PYMODULE'),
  ('openpyxl.workbook._writer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\workbook\\_writer.py',
   'PYMODULE'),
  ('openpyxl.worksheet._writer',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\_writer.py',
   'PYMODULE'),
  ('openpyxl.packaging.extended',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\packaging\\extended.py',
   'PYMODULE'),
  ('openpyxl.worksheet.copier',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\copier.py',
   'PYMODULE'),
  ('openpyxl.worksheet._write_only',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\openpyxl\\worksheet\\_write_only.py',
   'PYMODULE')],
 [('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('mkl_msg.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_msg.dll',
   'BINARY'),
  ('msvcp140_atomic_wait.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_atomic_wait.dll',
   'BINARY'),
  ('tbbmalloc.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tbbmalloc.dll',
   'BINARY'),
  ('mkl_sequential.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_sequential.2.dll',
   'BINARY'),
  ('msvcp140_2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_2.dll',
   'BINARY'),
  ('mkl_scalapack_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_scalapack_ilp64.2.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('mkl_blacs_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_ilp64.2.dll',
   'BINARY'),
  ('omptarget.sycl.wrap.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.sycl.wrap.dll',
   'BINARY'),
  ('mkl_vml_avx.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_avx.2.dll',
   'BINARY'),
  ('ucrtbase.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ucrtbase.dll',
   'BINARY'),
  ('mkl_vml_mc.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_mc.2.dll',
   'BINARY'),
  ('omptarget.rtl.level0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.rtl.level0.dll',
   'BINARY'),
  ('msvcp140_codecvt_ids.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_codecvt_ids.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('tbbmalloc_proxy.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tbbmalloc_proxy.dll',
   'BINARY'),
  ('mkl_def.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_def.2.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_intelmpi_ilp64.2.dll',
   'BINARY'),
  ('vcruntime140_threads.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcruntime140_threads.dll',
   'BINARY'),
  ('mkl_intel_thread.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_intel_thread.2.dll',
   'BINARY'),
  ('mkl_avx512.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_avx512.2.dll',
   'BINARY'),
  ('omptarget.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.dll',
   'BINARY'),
  ('zlib.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\zlib.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('mkl_cdft_core.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_cdft_core.2.dll',
   'BINARY'),
  ('vcomp140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcomp140.dll',
   'BINARY'),
  ('ffi.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ffi.dll',
   'BINARY'),
  ('sqlite3.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\sqlite3.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('tcl86t.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tcl86t.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-console-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('mkl_mc3.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_mc3.2.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-multibyte-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-multibyte-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('ffi-8.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ffi-8.dll',
   'BINARY'),
  ('concrt140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\concrt140.dll',
   'BINARY'),
  ('vcruntime140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcruntime140.dll',
   'BINARY'),
  ('bzip2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\bzip2.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_msmpi_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('mkl_scalapack_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_scalapack_lp64.2.dll',
   'BINARY'),
  ('mkl_avx.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_avx.2.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('vccorlib140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vccorlib140.dll',
   'BINARY'),
  ('tk86t.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tk86t.dll',
   'BINARY'),
  ('mkl_rt.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_rt.2.dll',
   'BINARY'),
  ('mkl_blacs_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_lp64.2.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('ffi-7.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\ffi-7.dll',
   'BINARY'),
  ('mkl_blacs_intelmpi_lp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_intelmpi_lp64.2.dll',
   'BINARY'),
  ('mkl_vml_def.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_def.2.dll',
   'BINARY'),
  ('vcruntime140_1.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcruntime140_1.dll',
   'BINARY'),
  ('libiompstubs5md.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libiompstubs5md.dll',
   'BINARY'),
  ('vcamp140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\vcamp140.dll',
   'BINARY'),
  ('api-ms-win-core-fibers-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-fibers-l1-1-0.dll',
   'BINARY'),
  ('mkl_vml_avx512.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_avx512.2.dll',
   'BINARY'),
  ('mkl_blacs_msmpi_ilp64.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_blacs_msmpi_ilp64.2.dll',
   'BINARY'),
  ('mkl_tbb_thread.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_tbb_thread.2.dll',
   'BINARY'),
  ('mkl_vml_mc3.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_mc3.2.dll',
   'BINARY'),
  ('mkl_avx2.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_avx2.2.dll',
   'BINARY'),
  ('libiomp5md.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libiomp5md.dll',
   'BINARY'),
  ('omptarget.rtl.opencl.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\omptarget.rtl.opencl.dll',
   'BINARY'),
  ('msvcp140.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-private-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-private-l1-1-0.dll',
   'BINARY'),
  ('mkl_pgi_thread.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_pgi_thread.2.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('msvcp140_1.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\msvcp140_1.dll',
   'BINARY'),
  ('libimalloc.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libimalloc.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('mkl_core.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_core.2.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('libbz2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libbz2.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('mkl_mc.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_mc.2.dll',
   'BINARY'),
  ('mkl_vml_cmpt.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_cmpt.2.dll',
   'BINARY'),
  ('tbb12.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tbb12.dll',
   'BINARY'),
  ('mkl_vml_avx2.2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\mkl_vml_avx2.2.dll',
   'BINARY'),
  ('libexpat.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libexpat.dll',
   'BINARY'),
  ('python311.dll', 'D:\\Users\\ZhangLan\\miniconda3\\python311.dll', 'BINARY'),
  ('libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('_decimal.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('select.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\select.pyd',
   'EXTENSION'),
  ('_socket.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_socket.pyd',
   'EXTENSION'),
  ('_lzma.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_lzma.pyd',
   'EXTENSION'),
  ('_bz2.pyd', 'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('win32\\win32api.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('_ssl.pyd', 'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_queue.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_queue.pyd',
   'EXTENSION'),
  ('_ctypes.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_ctypes.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('pyexpat.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\pyexpat.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('_elementtree.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\DLLs\\_elementtree.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_tests.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('psutil\\_psutil_windows.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\psutil\\_psutil_windows.pyd',
   'EXTENSION'),
  ('numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\core\\_multiarray_umath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32pdh.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\win32pdh.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\lapack_lite.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\mtrand.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_sfc64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_philox.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_philox.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_pcg64.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_mt19937.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\bit_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_generator.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_generator.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_bounded_integers.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\random\\_common.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\random\\_common.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\fft\\_pocketfft_internal.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\linalg\\_umath_linalg.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_webp.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_webp.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingtk.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imagingtk.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingcms.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imagingcms.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imagingmath.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imagingmath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('PIL\\_imaging.cp311-win_amd64.pyd',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\PIL\\_imaging.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\win32\\pywintypes311.dll',
   'BINARY'),
  ('python3.dll', 'D:\\Users\\ZhangLan\\miniconda3\\python3.dll', 'BINARY'),
  ('numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\numpy\\.libs\\libopenblas64__v0.3.21-gcc_10_3_0.dll',
   'BINARY'),
  ('libwebpmux.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libwebpmux.dll',
   'BINARY'),
  ('libwebpdemux.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libwebpdemux.dll',
   'BINARY'),
  ('libwebp.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libwebp.dll',
   'BINARY'),
  ('lcms2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\lcms2.dll',
   'BINARY'),
  ('openjp2.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\openjp2.dll',
   'BINARY'),
  ('tiff.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\tiff.dll',
   'BINARY'),
  ('libsharpyuv.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\libsharpyuv.dll',
   'BINARY'),
  ('deflate.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\deflate.dll',
   'BINARY'),
  ('Lerc.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\Lerc.dll',
   'BINARY'),
  ('zstd.dll',
   'D:\\Users\\ZhangLan\\miniconda3\\Library\\bin\\zstd.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'D:\\salary_reports\\build\\main\\base_library.zip',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\top_level.txt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\top_level.txt',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\RECORD',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\RECORD',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\LICENSE',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\LICENSE',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\METADATA',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\METADATA',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\INSTALLER',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\INSTALLER',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\direct_url.json',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\direct_url.json',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\WHEEL',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\WHEEL',
   'DATA'),
  ('importlib_metadata-7.1.0.dist-info\\REQUESTED',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\importlib_metadata-7.1.0.dist-info\\REQUESTED',
   'DATA'),
  ('wheel-0.43.0.dist-info\\INSTALLER',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\INSTALLER',
   'DATA'),
  ('wheel-0.43.0.dist-info\\METADATA',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\METADATA',
   'DATA'),
  ('wheel-0.43.0.dist-info\\entry_points.txt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\entry_points.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\WHEEL',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\WHEEL',
   'DATA'),
  ('wheel-0.43.0.dist-info\\LICENSE.txt',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\LICENSE.txt',
   'DATA'),
  ('wheel-0.43.0.dist-info\\RECORD',
   'D:\\Users\\ZhangLan\\miniconda3\\Lib\\site-packages\\wheel-0.43.0.dist-info\\RECORD',
   'DATA')])
